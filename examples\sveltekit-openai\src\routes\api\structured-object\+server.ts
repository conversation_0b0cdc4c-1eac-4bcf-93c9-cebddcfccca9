import { streamObject } from 'ai';
import { notificationSchema } from '../../structured-object/schema.js';
import { createOpenAI } from '@ai-sdk/openai';
import { env } from '$env/dynamic/private';

const openai = createOpenAI({
  apiKey: "sk-AA6yOd2Nh9hpBdogB8A2C76bBf7b44E984AcE84973Dd39Cd",
  baseURL: "https://aihubmix.com/v1",
});

export async function POST({ request }: { request: Request }) {
  const context = await request.json();

  const result = streamObject({
    model: openai('kimi-k2-0711-preview'),
    schema: notificationSchema,
    prompt:
      `Generate 3 notifications for a messages app in this context:` + context,
    onError: error => {
      console.error(error);
    },
  });

  return result.toTextStreamResponse();
}
