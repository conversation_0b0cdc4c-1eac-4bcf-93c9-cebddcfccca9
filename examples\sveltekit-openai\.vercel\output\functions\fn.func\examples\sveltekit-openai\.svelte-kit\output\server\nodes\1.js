

export const index = 1;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/fallbacks/error.svelte.js')).default;
export const imports = ["_app/immutable/nodes/1.isyMjBx1.js","_app/immutable/chunks/CGanT4Ze.js","_app/immutable/chunks/Cc9lkQ6R.js","_app/immutable/chunks/CsOTzF8v.js","_app/immutable/chunks/2Hh1nz_V.js","_app/immutable/chunks/CRU_BPGW.js","_app/immutable/chunks/Bq-mu1H_.js","_app/immutable/chunks/CaKqO4GF.js","_app/immutable/chunks/BwxiO7wK.js"];
export const stylesheets = [];
export const fonts = [];
