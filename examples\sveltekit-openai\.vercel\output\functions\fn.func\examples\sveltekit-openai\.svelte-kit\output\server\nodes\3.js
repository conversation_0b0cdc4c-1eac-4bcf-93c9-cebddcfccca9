

export const index = 3;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/chat/_page.svelte.js')).default;
export const imports = ["_app/immutable/nodes/3.Djxk6jL9.js","_app/immutable/chunks/CGanT4Ze.js","_app/immutable/chunks/Cc9lkQ6R.js","_app/immutable/chunks/2Hh1nz_V.js","_app/immutable/chunks/0U5e1eCd.js","_app/immutable/chunks/zbNWLoGC.js","_app/immutable/chunks/krzcCheT.js","_app/immutable/chunks/BrOQj_DL.js","_app/immutable/chunks/Decf1TKS.js","_app/immutable/chunks/CEXehBSS.js","_app/immutable/chunks/BjpmZ2ke.js","_app/immutable/chunks/BwxiO7wK.js","_app/immutable/chunks/U6ZYfsmc.js"];
export const stylesheets = ["_app/immutable/assets/StreamingMarkdown.BsuaRF1j.css"];
export const fonts = [];
