

export const index = 5;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/completion/_page.svelte.js')).default;
export const imports = ["_app/immutable/nodes/5.ayhg-geQ.js","_app/immutable/chunks/CGanT4Ze.js","_app/immutable/chunks/Cc9lkQ6R.js","_app/immutable/chunks/CsOTzF8v.js","_app/immutable/chunks/2Hh1nz_V.js","_app/immutable/chunks/0U5e1eCd.js","_app/immutable/chunks/CRU_BPGW.js","_app/immutable/chunks/CEXehBSS.js","_app/immutable/chunks/krzcCheT.js","_app/immutable/chunks/vHm_wtdr.js","_app/immutable/chunks/DplMobLG.js"];
export const stylesheets = [];
export const fonts = [];
