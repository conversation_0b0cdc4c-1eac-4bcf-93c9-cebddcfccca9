import{c as be,a as P,t as ne}from"./CGanT4Ze.js";import{p as ge,f as me,a as he,c as L,A as Q,r as U,t as W}from"./Cc9lkQ6R.js";import{s as F}from"./Decf1TKS.js";import{p as N,i as je,b as X,r as Ae}from"./0U5e1eCd.js";import{t as we,e as xe,c as Y,b as Z}from"./krzcCheT.js";var $=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,h=e=>!e||typeof e!="object"||Object.keys(e).length===0,Ve=(e,a)=>JSON.stringify(e)===JSON.stringify(a);function ae(e,a){e.forEach(function(t){Array.isArray(t)?ae(t,a):a.push(t)})}function oe(e){let a=[];return ae(e,a),a}var ie=(...e)=>oe(e).filter(Boolean),se=(e,a)=>{let t={},V=Object.keys(e),k=Object.keys(a);for(let c of V)if(k.includes(c)){let m=e[c],A=a[c];Array.isArray(m)||Array.isArray(A)?t[c]=ie(A,m):typeof m=="object"&&typeof A=="object"?t[c]=se(m,A):t[c]=A+" "+m}else t[c]=e[c];for(let c of k)V.includes(c)||(t[c]=a[c]);return t},ee=e=>!e||typeof e!="string"?e:e.replace(/\s+/g," ").trim(),ke={twMerge:!0,twMergeConfig:{},responsiveVariants:!1},le=e=>e||void 0,S=(...e)=>le(oe(e).filter(Boolean).join(" ")),J=null,_={},R=!1,G=(...e)=>a=>a.twMerge?((!J||R)&&(R=!1,J=h(_)?we:xe({..._,extend:{theme:_.theme,classGroups:_.classGroups,conflictingClassGroupModifiers:_.conflictingClassGroupModifiers,conflictingClassGroups:_.conflictingClassGroups,..._.extend}})),le(J(S(e)))):S(e),te=(e,a)=>{for(let t in a)e.hasOwnProperty(t)?e[t]=S(e[t],a[t]):e[t]=a[t];return e},Oe=(e,a)=>{let{extend:t=null,slots:V={},variants:k={},compoundVariants:c=[],compoundSlots:m=[],defaultVariants:A={}}=e,p={...ke,...a},C=t!=null&&t.base?S(t.base,e==null?void 0:e.base):e==null?void 0:e.base,b=t!=null&&t.variants&&!h(t.variants)?se(k,t.variants):k,M=t!=null&&t.defaultVariants&&!h(t.defaultVariants)?{...t.defaultVariants,...A}:A;!h(p.twMergeConfig)&&!Ve(p.twMergeConfig,_)&&(R=!0,_=p.twMergeConfig);let w=h(t==null?void 0:t.slots),g=h(V)?{}:{base:S(e==null?void 0:e.base,w&&(t==null?void 0:t.base)),...V},x=w?g:te({...t==null?void 0:t.slots},h(g)?{base:e==null?void 0:e.base}:g),O=h(t==null?void 0:t.compoundVariants)?c:ie(t==null?void 0:t.compoundVariants,c),d=v=>{if(h(b)&&h(V)&&w)return G(C,v==null?void 0:v.class,v==null?void 0:v.className)(p);if(O&&!Array.isArray(O))throw new TypeError(`The "compoundVariants" prop must be an array. Received: ${typeof O}`);if(m&&!Array.isArray(m))throw new TypeError(`The "compoundSlots" prop must be an array. Received: ${typeof m}`);let ue=(r,o,n=[],s)=>{let i=n;if(typeof o=="string")i=i.concat(ee(o).split(" ").map(l=>`${r}:${l}`));else if(Array.isArray(o))i=i.concat(o.reduce((l,f)=>l.concat(`${r}:${f}`),[]));else if(typeof o=="object"&&typeof s=="string"){for(let l in o)if(o.hasOwnProperty(l)&&l===s){let f=o[l];if(f&&typeof f=="string"){let u=ee(f);i[s]?i[s]=i[s].concat(u.split(" ").map(y=>`${r}:${y}`)):i[s]=u.split(" ").map(y=>`${r}:${y}`)}else Array.isArray(f)&&f.length>0&&(i[s]=f.reduce((u,y)=>u.concat(`${r}:${y}`),[]))}}return i},K=(r,o=b,n=null,s=null)=>{var i;let l=o[r];if(!l||h(l))return null;let f=(i=s==null?void 0:s[r])!=null?i:v==null?void 0:v[r];if(f===null)return null;let u=$(f),y=Array.isArray(p.responsiveVariants)&&p.responsiveVariants.length>0||p.responsiveVariants===!0,z=M==null?void 0:M[r],j=[];if(typeof u=="object"&&y)for(let[E,I]of Object.entries(u)){let ye=l[I];if(E==="initial"){z=I;continue}Array.isArray(p.responsiveVariants)&&!p.responsiveVariants.includes(E)||(j=ue(E,ye,j,n))}let T=u!=null&&typeof u!="object"?u:$(z),B=l[T||"false"];return typeof j=="object"&&typeof n=="string"&&j[n]?te(j,B):j.length>0?(j.push(B),n==="base"?j.join(" "):j):B},ce=()=>b?Object.keys(b).map(r=>K(r,b)):null,de=(r,o)=>{if(!b||typeof b!="object")return null;let n=new Array;for(let s in b){let i=K(s,b,r,o),l=r==="base"&&typeof i=="string"?i:i&&i[r];l&&(n[n.length]=l)}return n},q={};for(let r in v)v[r]!==void 0&&(q[r]=v[r]);let D=(r,o)=>{var n;let s=typeof(v==null?void 0:v[r])=="object"?{[r]:(n=v[r])==null?void 0:n.initial}:{};return{...M,...q,...s,...o}},H=(r=[],o)=>{let n=[];for(let{class:s,className:i,...l}of r){let f=!0;for(let[u,y]of Object.entries(l)){let z=D(u,o)[u];if(Array.isArray(y)){if(!y.includes(z)){f=!1;break}}else{let j=T=>T==null||T===!1;if(j(y)&&j(z))continue;if(z!==y){f=!1;break}}}f&&(s&&n.push(s),i&&n.push(i))}return n},pe=r=>{let o=H(O,r);if(!Array.isArray(o))return o;let n={};for(let s of o)if(typeof s=="string"&&(n.base=G(n.base,s)(p)),typeof s=="object")for(let[i,l]of Object.entries(s))n[i]=G(n[i],l)(p);return n},ve=r=>{if(m.length<1)return null;let o={};for(let{slots:n=[],class:s,className:i,...l}of m){if(!h(l)){let f=!0;for(let u of Object.keys(l)){let y=D(u,r)[u];if(y===void 0||(Array.isArray(l[u])?!l[u].includes(y):l[u]!==y)){f=!1;break}}if(!f)continue}for(let f of n)o[f]=o[f]||[],o[f].push([s,i])}return o};if(!h(V)||!w){let r={};if(typeof x=="object"&&!h(x))for(let o of Object.keys(x))r[o]=n=>{var s,i;return G(x[o],de(o,n),((s=pe(n))!=null?s:[])[o],((i=ve(n))!=null?i:[])[o],n==null?void 0:n.class,n==null?void 0:n.className)(p)};return r}return G(C,ce(),H(O),v==null?void 0:v.class,v==null?void 0:v.className)(p)},fe=()=>{if(!(!b||typeof b!="object"))return Object.keys(b)};return d.variantKeys=fe(),d.extend=t,d.base=C,d.slots=x,d.variants=b,d.defaultVariants=M,d.compoundSlots=m,d.compoundVariants=O,d};const re=Oe({base:"ring-offset-background focus-visible:ring-ring inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border-input bg-background hover:bg-accent hover:text-accent-foreground border",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}});var _e=ne("<a><!></a>"),Me=ne("<button><!></button>");function Te(e,a){ge(a,!0);let t=N(a,"variant",3,"default"),V=N(a,"size",3,"default"),k=N(a,"ref",15,null),c=N(a,"href",3,void 0),m=N(a,"type",3,"button"),A=Ae(a,["$$slots","$$events","$$legacy","class","variant","size","ref","href","type","children"]);var p=be(),C=me(p);{var b=w=>{var g=_e();let x;var O=L(g);F(O,()=>a.children??Q),U(g),X(g,d=>k(d),()=>k()),W(d=>x=Z(g,x,{class:d,href:c(),...A}),[()=>Y(re({variant:t(),size:V()}),a.class)]),P(w,g)},M=w=>{var g=Me();let x;var O=L(g);F(O,()=>a.children??Q),U(g),X(g,d=>k(d),()=>k()),W(d=>x=Z(g,x,{class:d,type:m(),...A}),[()=>Y(re({variant:t(),size:V()}),a.class)]),P(w,g)};je(C,w=>{c()?w(b):w(M,!1)})}P(e,p),he()}export{Te as B};
