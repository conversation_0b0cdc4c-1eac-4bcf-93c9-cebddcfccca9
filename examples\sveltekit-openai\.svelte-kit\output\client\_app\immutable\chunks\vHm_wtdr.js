var d=Object.defineProperty;var g=e=>{throw TypeError(e)};var x=(e,t,o)=>t in e?d(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o;var m=(e,t,o)=>x(e,typeof t!="symbol"?t+"":t,o),h=(e,t,o)=>t.has(e)||g("Cannot "+o);var s=(e,t,o)=>(h(e,t,"read from private field"),o?o.call(e):t.get(e)),i=(e,t,o)=>t.has(e)?g("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,o);import{d as l,e as c,g as p,b as C}from"./Cc9lkQ6R.js";import{c as u,K as S,S as K}from"./DplMobLG.js";var r,a,n;class f{constructor(){m(this,"completions",new K);i(this,r,l(c([])));i(this,a,l(!1));i(this,n,l())}get data(){return p(s(this,r))}set data(t){C(s(this,r),t,!0)}get loading(){return p(s(this,a))}set loading(t){C(s(this,a),t,!0)}get error(){return p(s(this,n))}set error(t){C(s(this,n),t,!0)}}r=new WeakMap,a=new WeakMap,n=new WeakMap;class w extends S{constructor(t){super(f,t)}}const{hasContext:M,getContext:j,setContext:k}=u("Completion");export{w as K,j as g,M as h,k as s};
