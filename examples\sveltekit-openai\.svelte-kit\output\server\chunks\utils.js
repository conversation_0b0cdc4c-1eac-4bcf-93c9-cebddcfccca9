var is_array = Array.isArray;
var index_of = Array.prototype.indexOf;
var array_from = Array.from;
var define_property = Object.defineProperty;
var get_descriptor = Object.getOwnPropertyDescriptor;
var object_prototype = Object.prototype;
var array_prototype = Array.prototype;
var get_prototype_of = Object.getPrototypeOf;
var is_extensible = Object.isExtensible;
const noop = () => {
};
function run(fn) {
  return fn();
}
function run_all(arr) {
  for (var i = 0; i < arr.length; i++) {
    arr[i]();
  }
}
export {
  array_prototype as a,
  get_prototype_of as b,
  is_extensible as c,
  index_of as d,
  define_property as e,
  array_from as f,
  get_descriptor as g,
  run as h,
  is_array as i,
  noop as n,
  object_prototype as o,
  run_all as r
};
