import{y as Be,B as Fe,z as _e,ae as Ue,C as $,aG as We,aH as He,I as Ye,aI as qe,T as me,aJ as Je}from"./Cc9lkQ6R.js";import{i as Ke,c as De,d as Xe,a as Qe,n as Ze,b as er}from"./2Hh1nz_V.js";function rr(e,o){var r=void 0,t;Be(()=>{r!==(r=o())&&(t&&(Fe(t),t=null),r&&(t=_e(()=>{Ue(()=>r(e))})))})}function ze(e){var o,r,t="";if(typeof e=="string"||typeof e=="number")t+=e;else if(typeof e=="object")if(Array.isArray(e)){var s=e.length;for(o=0;o<s;o++)e[o]&&(r=ze(e[o]))&&(t&&(t+=" "),t+=r)}else for(r in e)e[r]&&(t&&(t+=" "),t+=r);return t}function Me(){for(var e,o,r=0,t="",s=arguments.length;r<s;r++)(e=arguments[r])&&(o=ze(e))&&(t&&(t+=" "),t+=o);return t}function tr(e){return typeof e=="object"?Me(e):e??""}const we=[...` 	
\r\f \v\uFEFF`];function or(e,o,r){var t=e==null?"":""+e;if(r){for(var s in r)if(r[s])t=t?t+" "+s:s;else if(t.length)for(var n=s.length,i=0;(i=t.indexOf(s,i))>=0;){var u=i+n;(i===0||we.includes(t[i-1]))&&(u===t.length||we.includes(t[u]))?t=(i===0?"":t.substring(0,i))+t.substring(u+1):i=u}}return t===""?null:t}function ve(e,o=!1){var r=o?" !important;":";",t="";for(var s in e){var n=e[s];n!=null&&n!==""&&(t+=" "+s+": "+n+r)}return t}function oe(e){return e[0]!=="-"||e[1]!=="-"?e.toLowerCase():e}function sr(e,o){if(o){var r="",t,s;if(Array.isArray(o)?(t=o[0],s=o[1]):t=o,e){e=String(e).replaceAll(/\s*\/\*.*?\*\/\s*/g,"").trim();var n=!1,i=0,u=!1,d=[];t&&d.push(...Object.keys(t).map(oe)),s&&d.push(...Object.keys(s).map(oe));var p=0,w=-1;const z=e.length;for(var v=0;v<z;v++){var A=e[v];if(u?A==="/"&&e[v-1]==="*"&&(u=!1):n?n===A&&(n=!1):A==="/"&&e[v+1]==="*"?u=!0:A==='"'||A==="'"?n=A:A==="("?i++:A===")"&&i--,!u&&n===!1&&i===0){if(A===":"&&w===-1)w=v;else if(A===";"||v===z-1){if(w!==-1){var I=oe(e.substring(p,w).trim());if(!d.includes(I)){A!==";"&&v++;var S=e.substring(p,v).trim();r+=" "+S+";"}}p=v+1,w=-1}}}}return t&&(r+=ve(t)),s&&(r+=ve(s,!0)),r=r.trim(),r===""?null:r}return e==null?null:String(e)}function nr(e,o,r,t,s,n){var i=e.__className;if($||i!==r||i===void 0){var u=or(r,t,n);(!$||u!==e.getAttribute("class"))&&(u==null?e.removeAttribute("class"):o?e.className=u:e.setAttribute("class",u)),e.__className=r}else if(n&&s!==n)for(var d in n){var p=!!n[d];(s==null||p!==!!s[d])&&e.classList.toggle(d,p)}return n}function se(e,o={},r,t){for(var s in r){var n=r[s];o[s]!==n&&(r[s]==null?e.style.removeProperty(s):e.style.setProperty(s,n,t))}}function ir(e,o,r,t){var s=e.__style;if($||s!==o){var n=sr(o,t);(!$||n!==e.getAttribute("style"))&&(n==null?e.removeAttribute("style"):e.style.cssText=n),e.__style=o}else t&&(Array.isArray(t)?(se(e,r==null?void 0:r[0],t[0]),se(e,r==null?void 0:r[1],t[1],"important")):se(e,r,t));return t}const F=Symbol("class"),_=Symbol("style"),Ce=Symbol("is custom element"),Ge=Symbol("is html");function lr(e,o){o?e.hasAttribute("selected")||e.setAttribute("selected",""):e.removeAttribute("selected")}function ye(e,o,r,t){var s=Ie(e);$&&(s[o]=e.getAttribute(o),o==="src"||o==="srcset"||o==="href"&&e.nodeName==="LINK")||s[o]!==(s[o]=r)&&(o==="loading"&&(e[qe]=r),r==null?e.removeAttribute(o):typeof r!="string"&&Pe(e).includes(o)?e[o]=r:e.setAttribute(o,r))}function Dr(e,o,r,t,s=!1){var n=Ie(e),i=n[Ce],u=!n[Ge];let d=$&&i;d&&me(!1);var p=o||{},w=e.tagName==="OPTION";for(var v in o)v in r||(r[v]=null);r.class?r.class=tr(r.class):r[F]&&(r.class=null),r[_]&&(r.style??(r.style=null));var A=Pe(e);for(const b in r){let g=r[b];if(w&&b==="value"&&g==null){e.value=e.__value="",p[b]=g;continue}if(b==="class"){var I=e.namespaceURI==="http://www.w3.org/1999/xhtml";nr(e,I,g,t,o==null?void 0:o[F],r[F]),p[b]=g,p[F]=r[F];continue}if(b==="style"){ir(e,g,o==null?void 0:o[_],r[_]),p[b]=g,p[_]=r[_];continue}var S=p[b];if(g!==S){p[b]=g;var z=b[0]+b[1];if(z!=="$$")if(z==="on"){const x={},c="$$"+b;let h=b.slice(2);var G=er(h);if(Ke(h)&&(h=h.slice(0,-7),x.capture=!0),!G&&S){if(g!=null)continue;e.removeEventListener(h,p[c],x),p[c]=null}if(g!=null)if(G)e[`__${h}`]=g,Xe([h]);else{let W=function(H){p[b].call(this,H)};p[c]=De(h,e,W,x)}else G&&(e[`__${h}`]=void 0)}else if(b==="style")ye(e,b,g);else if(b==="autofocus")Qe(e,!!g);else if(!i&&(b==="__value"||b==="value"&&g!=null))e.value=e.__value=g;else if(b==="selected"&&w)lr(e,g);else{var y=b;u||(y=Ze(y));var T=y==="defaultValue"||y==="defaultChecked";if(g==null&&!i&&!T)if(n[b]=null,y==="value"||y==="checked"){let x=e;const c=o===void 0;if(y==="value"){let h=x.defaultValue;x.removeAttribute(y),x.defaultValue=h,x.value=x.__value=c?h:null}else{let h=x.defaultChecked;x.removeAttribute(y),x.defaultChecked=h,x.checked=c?h:!1}}else e.removeAttribute(b);else T||A.includes(y)&&(i||typeof g!="string")?e[y]=g:typeof g!="function"&&ye(e,y,g)}}}d&&me(!0);for(let b of Object.getOwnPropertySymbols(r))b.description===We&&rr(e,()=>r[b]);return p}function Ie(e){return e.__attributes??(e.__attributes={[Ce]:e.nodeName.includes("-"),[Ge]:e.namespaceURI===He})}var xe=new Map;function Pe(e){var o=xe.get(e.nodeName);if(o)return o;xe.set(e.nodeName,o=[]);for(var r,t=e,s=Element.prototype;s!==t;){r=Je(t);for(var n in r)r[n].set&&o.push(n);t=Ye(t)}return o}const ue="-",ar=e=>{const o=dr(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:t}=e;return{getClassGroupId:i=>{const u=i.split(ue);return u[0]===""&&u.length!==1&&u.shift(),Te(u,o)||cr(i)},getConflictingClassGroupIds:(i,u)=>{const d=r[i]||[];return u&&t[i]?[...d,...t[i]]:d}}},Te=(e,o)=>{var i;if(e.length===0)return o.classGroupId;const r=e[0],t=o.nextPart.get(r),s=t?Te(e.slice(1),t):void 0;if(s)return s;if(o.validators.length===0)return;const n=e.join(ue);return(i=o.validators.find(({validator:u})=>u(n)))==null?void 0:i.classGroupId},Ae=/^\[(.+)\]$/,cr=e=>{if(Ae.test(e)){const o=Ae.exec(e)[1],r=o==null?void 0:o.substring(0,o.indexOf(":"));if(r)return"arbitrary.."+r}},dr=e=>{const{theme:o,classGroups:r}=e,t={nextPart:new Map,validators:[]};for(const s in r)ie(r[s],t,s,o);return t},ie=(e,o,r,t)=>{e.forEach(s=>{if(typeof s=="string"){const n=s===""?o:ke(o,s);n.classGroupId=r;return}if(typeof s=="function"){if(ur(s)){ie(s(t),o,r,t);return}o.validators.push({validator:s,classGroupId:r});return}Object.entries(s).forEach(([n,i])=>{ie(i,ke(o,n),r,t)})})},ke=(e,o)=>{let r=e;return o.split(ue).forEach(t=>{r.nextPart.has(t)||r.nextPart.set(t,{nextPart:new Map,validators:[]}),r=r.nextPart.get(t)}),r},ur=e=>e.isThemeGetter,fr=e=>{if(e<1)return{get:()=>{},set:()=>{}};let o=0,r=new Map,t=new Map;const s=(n,i)=>{r.set(n,i),o++,o>e&&(o=0,t=r,r=new Map)};return{get(n){let i=r.get(n);if(i!==void 0)return i;if((i=t.get(n))!==void 0)return s(n,i),i},set(n,i){r.has(n)?r.set(n,i):s(n,i)}}},le="!",ae=":",pr=ae.length,br=e=>{const{prefix:o,experimentalParseClassName:r}=e;let t=s=>{const n=[];let i=0,u=0,d=0,p;for(let S=0;S<s.length;S++){let z=s[S];if(i===0&&u===0){if(z===ae){n.push(s.slice(d,S)),d=S+pr;continue}if(z==="/"){p=S;continue}}z==="["?i++:z==="]"?i--:z==="("?u++:z===")"&&u--}const w=n.length===0?s:s.substring(d),v=gr(w),A=v!==w,I=p&&p>d?p-d:void 0;return{modifiers:n,hasImportantModifier:A,baseClassName:v,maybePostfixModifierPosition:I}};if(o){const s=o+ae,n=t;t=i=>i.startsWith(s)?n(i.substring(s.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:i,maybePostfixModifierPosition:void 0}}if(r){const s=t;t=n=>r({className:n,parseClassName:s})}return t},gr=e=>e.endsWith(le)?e.substring(0,e.length-1):e.startsWith(le)?e.substring(1):e,hr=e=>{const o=Object.fromEntries(e.orderSensitiveModifiers.map(t=>[t,!0]));return t=>{if(t.length<=1)return t;const s=[];let n=[];return t.forEach(i=>{i[0]==="["||o[i]?(s.push(...n.sort(),i),n=[]):n.push(i)}),s.push(...n.sort()),s}},mr=e=>({cache:fr(e.cacheSize),parseClassName:br(e),sortModifiers:hr(e),...ar(e)}),wr=/\s+/,vr=(e,o)=>{const{parseClassName:r,getClassGroupId:t,getConflictingClassGroupIds:s,sortModifiers:n}=o,i=[],u=e.trim().split(wr);let d="";for(let p=u.length-1;p>=0;p-=1){const w=u[p],{isExternal:v,modifiers:A,hasImportantModifier:I,baseClassName:S,maybePostfixModifierPosition:z}=r(w);if(v){d=w+(d.length>0?" "+d:d);continue}let G=!!z,y=t(G?S.substring(0,z):S);if(!y){if(!G){d=w+(d.length>0?" "+d:d);continue}if(y=t(S),!y){d=w+(d.length>0?" "+d:d);continue}G=!1}const T=n(A).join(":"),b=I?T+le:T,g=b+y;if(i.includes(g))continue;i.push(g);const x=s(y,G);for(let c=0;c<x.length;++c){const h=x[c];i.push(b+h)}d=w+(d.length>0?" "+d:d)}return d};function yr(){let e=0,o,r,t="";for(;e<arguments.length;)(o=arguments[e++])&&(r=Re(o))&&(t&&(t+=" "),t+=r);return t}const Re=e=>{if(typeof e=="string")return e;let o,r="";for(let t=0;t<e.length;t++)e[t]&&(o=Re(e[t]))&&(r&&(r+=" "),r+=o);return r};function ce(e,...o){let r,t,s,n=i;function i(d){const p=o.reduce((w,v)=>v(w),e());return r=mr(p),t=r.cache.get,s=r.cache.set,n=u,u(d)}function u(d){const p=t(d);if(p)return p;const w=vr(d,r);return s(d,w),w}return function(){return n(yr.apply(null,arguments))}}const k=e=>{const o=r=>r[e]||[];return o.isThemeGetter=!0,o},Ne=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Le=/^\((?:(\w[\w-]*):)?(.+)\)$/i,xr=/^\d+\/\d+$/,Ar=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,kr=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Sr=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,zr=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Mr=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,O=e=>xr.test(e),f=e=>!!e&&!Number.isNaN(Number(e)),L=e=>!!e&&Number.isInteger(Number(e)),Se=e=>e.endsWith("%")&&f(e.slice(0,-1)),R=e=>Ar.test(e),Cr=()=>!0,Gr=e=>kr.test(e)&&!Sr.test(e),fe=()=>!1,Ir=e=>zr.test(e),Pr=e=>Mr.test(e),Tr=e=>!l(e)&&!a(e),Rr=e=>j(e,Oe,fe),l=e=>Ne.test(e),E=e=>j(e,$e,Gr),ne=e=>j(e,Ur,f),Nr=e=>j(e,Ee,fe),Lr=e=>j(e,Ve,Pr),Er=e=>j(e,fe,Ir),a=e=>Le.test(e),X=e=>B(e,$e),Vr=e=>B(e,Wr),Or=e=>B(e,Ee),$r=e=>B(e,Oe),jr=e=>B(e,Ve),Br=e=>B(e,Hr,!0),j=(e,o,r)=>{const t=Ne.exec(e);return t?t[1]?o(t[1]):r(t[2]):!1},B=(e,o,r=!1)=>{const t=Le.exec(e);return t?t[1]?o(t[1]):r:!1},Ee=e=>e==="position",Fr=new Set(["image","url"]),Ve=e=>Fr.has(e),_r=new Set(["length","size","percentage"]),Oe=e=>_r.has(e),$e=e=>e==="length",Ur=e=>e==="number",Wr=e=>e==="family-name",Hr=e=>e==="shadow",de=()=>{const e=k("color"),o=k("font"),r=k("text"),t=k("font-weight"),s=k("tracking"),n=k("leading"),i=k("breakpoint"),u=k("container"),d=k("spacing"),p=k("radius"),w=k("shadow"),v=k("inset-shadow"),A=k("drop-shadow"),I=k("blur"),S=k("perspective"),z=k("aspect"),G=k("ease"),y=k("animate"),T=()=>["auto","avoid","all","avoid-page","page","left","right","column"],b=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],g=()=>["auto","hidden","clip","visible","scroll"],x=()=>["auto","contain","none"],c=()=>[a,l,d],h=()=>[O,"full","auto",...c()],W=()=>[L,"none","subgrid",a,l],H=()=>["auto",{span:["full",L,a,l]},a,l],Y=()=>[L,"auto",a,l],pe=()=>["auto","min","max","fr",a,l],ee=()=>["start","end","center","between","around","evenly","stretch","baseline"],V=()=>["start","end","center","stretch"],P=()=>["auto",...c()],N=()=>[O,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...c()],m=()=>[e,a,l],re=()=>[Se,E],M=()=>["","none","full",p,a,l],C=()=>["",f,X,E],q=()=>["solid","dashed","dotted","double"],be=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],ge=()=>["","none",I,a,l],he=()=>["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",a,l],J=()=>["none",f,a,l],K=()=>["none",f,a,l],te=()=>[f,a,l],D=()=>[O,"full",...c()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[R],breakpoint:[R],color:[Cr],container:[R],"drop-shadow":[R],ease:["in","out","in-out"],font:[Tr],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[R],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[R],shadow:[R],spacing:["px",f],text:[R],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",O,l,a,z]}],container:["container"],columns:[{columns:[f,l,a,u]}],"break-after":[{"break-after":T()}],"break-before":[{"break-before":T()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...b(),l,a]}],overflow:[{overflow:g()}],"overflow-x":[{"overflow-x":g()}],"overflow-y":[{"overflow-y":g()}],overscroll:[{overscroll:x()}],"overscroll-x":[{"overscroll-x":x()}],"overscroll-y":[{"overscroll-y":x()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:h()}],"inset-x":[{"inset-x":h()}],"inset-y":[{"inset-y":h()}],start:[{start:h()}],end:[{end:h()}],top:[{top:h()}],right:[{right:h()}],bottom:[{bottom:h()}],left:[{left:h()}],visibility:["visible","invisible","collapse"],z:[{z:[L,"auto",a,l]}],basis:[{basis:[O,"full","auto",u,...c()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[f,O,"auto","initial","none",l]}],grow:[{grow:["",f,a,l]}],shrink:[{shrink:["",f,a,l]}],order:[{order:[L,"first","last","none",a,l]}],"grid-cols":[{"grid-cols":W()}],"col-start-end":[{col:H()}],"col-start":[{"col-start":Y()}],"col-end":[{"col-end":Y()}],"grid-rows":[{"grid-rows":W()}],"row-start-end":[{row:H()}],"row-start":[{"row-start":Y()}],"row-end":[{"row-end":Y()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":pe()}],"auto-rows":[{"auto-rows":pe()}],gap:[{gap:c()}],"gap-x":[{"gap-x":c()}],"gap-y":[{"gap-y":c()}],"justify-content":[{justify:[...ee(),"normal"]}],"justify-items":[{"justify-items":[...V(),"normal"]}],"justify-self":[{"justify-self":["auto",...V()]}],"align-content":[{content:["normal",...ee()]}],"align-items":[{items:[...V(),"baseline"]}],"align-self":[{self:["auto",...V(),"baseline"]}],"place-content":[{"place-content":ee()}],"place-items":[{"place-items":[...V(),"baseline"]}],"place-self":[{"place-self":["auto",...V()]}],p:[{p:c()}],px:[{px:c()}],py:[{py:c()}],ps:[{ps:c()}],pe:[{pe:c()}],pt:[{pt:c()}],pr:[{pr:c()}],pb:[{pb:c()}],pl:[{pl:c()}],m:[{m:P()}],mx:[{mx:P()}],my:[{my:P()}],ms:[{ms:P()}],me:[{me:P()}],mt:[{mt:P()}],mr:[{mr:P()}],mb:[{mb:P()}],ml:[{ml:P()}],"space-x":[{"space-x":c()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":c()}],"space-y-reverse":["space-y-reverse"],size:[{size:N()}],w:[{w:[u,"screen",...N()]}],"min-w":[{"min-w":[u,"screen","none",...N()]}],"max-w":[{"max-w":[u,"screen","none","prose",{screen:[i]},...N()]}],h:[{h:["screen",...N()]}],"min-h":[{"min-h":["screen","none",...N()]}],"max-h":[{"max-h":["screen",...N()]}],"font-size":[{text:["base",r,X,E]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[t,a,ne]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Se,l]}],"font-family":[{font:[Vr,l,o]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[s,a,l]}],"line-clamp":[{"line-clamp":[f,"none",a,ne]}],leading:[{leading:[n,...c()]}],"list-image":[{"list-image":["none",a,l]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",a,l]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:m()}],"text-color":[{text:m()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...q(),"wavy"]}],"text-decoration-thickness":[{decoration:[f,"from-font","auto",a,E]}],"text-decoration-color":[{decoration:m()}],"underline-offset":[{"underline-offset":[f,"auto",a,l]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:c()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",a,l]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",a,l]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...b(),Or,Nr]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","space","round"]}]}],"bg-size":[{bg:["auto","cover","contain",$r,Rr]}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},L,a,l],radial:["",a,l],conic:[L,a,l]},jr,Lr]}],"bg-color":[{bg:m()}],"gradient-from-pos":[{from:re()}],"gradient-via-pos":[{via:re()}],"gradient-to-pos":[{to:re()}],"gradient-from":[{from:m()}],"gradient-via":[{via:m()}],"gradient-to":[{to:m()}],rounded:[{rounded:M()}],"rounded-s":[{"rounded-s":M()}],"rounded-e":[{"rounded-e":M()}],"rounded-t":[{"rounded-t":M()}],"rounded-r":[{"rounded-r":M()}],"rounded-b":[{"rounded-b":M()}],"rounded-l":[{"rounded-l":M()}],"rounded-ss":[{"rounded-ss":M()}],"rounded-se":[{"rounded-se":M()}],"rounded-ee":[{"rounded-ee":M()}],"rounded-es":[{"rounded-es":M()}],"rounded-tl":[{"rounded-tl":M()}],"rounded-tr":[{"rounded-tr":M()}],"rounded-br":[{"rounded-br":M()}],"rounded-bl":[{"rounded-bl":M()}],"border-w":[{border:C()}],"border-w-x":[{"border-x":C()}],"border-w-y":[{"border-y":C()}],"border-w-s":[{"border-s":C()}],"border-w-e":[{"border-e":C()}],"border-w-t":[{"border-t":C()}],"border-w-r":[{"border-r":C()}],"border-w-b":[{"border-b":C()}],"border-w-l":[{"border-l":C()}],"divide-x":[{"divide-x":C()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":C()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...q(),"hidden","none"]}],"divide-style":[{divide:[...q(),"hidden","none"]}],"border-color":[{border:m()}],"border-color-x":[{"border-x":m()}],"border-color-y":[{"border-y":m()}],"border-color-s":[{"border-s":m()}],"border-color-e":[{"border-e":m()}],"border-color-t":[{"border-t":m()}],"border-color-r":[{"border-r":m()}],"border-color-b":[{"border-b":m()}],"border-color-l":[{"border-l":m()}],"divide-color":[{divide:m()}],"outline-style":[{outline:[...q(),"none","hidden"]}],"outline-offset":[{"outline-offset":[f,a,l]}],"outline-w":[{outline:["",f,X,E]}],"outline-color":[{outline:[e]}],shadow:[{shadow:["","none",w,Br,Er]}],"shadow-color":[{shadow:m()}],"inset-shadow":[{"inset-shadow":["none",a,l,v]}],"inset-shadow-color":[{"inset-shadow":m()}],"ring-w":[{ring:C()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:m()}],"ring-offset-w":[{"ring-offset":[f,E]}],"ring-offset-color":[{"ring-offset":m()}],"inset-ring-w":[{"inset-ring":C()}],"inset-ring-color":[{"inset-ring":m()}],opacity:[{opacity:[f,a,l]}],"mix-blend":[{"mix-blend":[...be(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":be()}],filter:[{filter:["","none",a,l]}],blur:[{blur:ge()}],brightness:[{brightness:[f,a,l]}],contrast:[{contrast:[f,a,l]}],"drop-shadow":[{"drop-shadow":["","none",A,a,l]}],grayscale:[{grayscale:["",f,a,l]}],"hue-rotate":[{"hue-rotate":[f,a,l]}],invert:[{invert:["",f,a,l]}],saturate:[{saturate:[f,a,l]}],sepia:[{sepia:["",f,a,l]}],"backdrop-filter":[{"backdrop-filter":["","none",a,l]}],"backdrop-blur":[{"backdrop-blur":ge()}],"backdrop-brightness":[{"backdrop-brightness":[f,a,l]}],"backdrop-contrast":[{"backdrop-contrast":[f,a,l]}],"backdrop-grayscale":[{"backdrop-grayscale":["",f,a,l]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[f,a,l]}],"backdrop-invert":[{"backdrop-invert":["",f,a,l]}],"backdrop-opacity":[{"backdrop-opacity":[f,a,l]}],"backdrop-saturate":[{"backdrop-saturate":[f,a,l]}],"backdrop-sepia":[{"backdrop-sepia":["",f,a,l]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":c()}],"border-spacing-x":[{"border-spacing-x":c()}],"border-spacing-y":[{"border-spacing-y":c()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",a,l]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[f,"initial",a,l]}],ease:[{ease:["linear","initial",G,a,l]}],delay:[{delay:[f,a,l]}],animate:[{animate:["none",y,a,l]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[S,a,l]}],"perspective-origin":[{"perspective-origin":he()}],rotate:[{rotate:J()}],"rotate-x":[{"rotate-x":J()}],"rotate-y":[{"rotate-y":J()}],"rotate-z":[{"rotate-z":J()}],scale:[{scale:K()}],"scale-x":[{"scale-x":K()}],"scale-y":[{"scale-y":K()}],"scale-z":[{"scale-z":K()}],"scale-3d":["scale-3d"],skew:[{skew:te()}],"skew-x":[{"skew-x":te()}],"skew-y":[{"skew-y":te()}],transform:[{transform:[a,l,"","none","gpu","cpu"]}],"transform-origin":[{origin:he()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:D()}],"translate-x":[{"translate-x":D()}],"translate-y":[{"translate-y":D()}],"translate-z":[{"translate-z":D()}],"translate-none":["translate-none"],accent:[{accent:m()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:m()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",a,l]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":c()}],"scroll-mx":[{"scroll-mx":c()}],"scroll-my":[{"scroll-my":c()}],"scroll-ms":[{"scroll-ms":c()}],"scroll-me":[{"scroll-me":c()}],"scroll-mt":[{"scroll-mt":c()}],"scroll-mr":[{"scroll-mr":c()}],"scroll-mb":[{"scroll-mb":c()}],"scroll-ml":[{"scroll-ml":c()}],"scroll-p":[{"scroll-p":c()}],"scroll-px":[{"scroll-px":c()}],"scroll-py":[{"scroll-py":c()}],"scroll-ps":[{"scroll-ps":c()}],"scroll-pe":[{"scroll-pe":c()}],"scroll-pt":[{"scroll-pt":c()}],"scroll-pr":[{"scroll-pr":c()}],"scroll-pb":[{"scroll-pb":c()}],"scroll-pl":[{"scroll-pl":c()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",a,l]}],fill:[{fill:["none",...m()]}],"stroke-w":[{stroke:[f,X,E,ne]}],stroke:[{stroke:["none",...m()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["before","after","placeholder","file","marker","selection","first-line","first-letter","backdrop","*","**"]}},Yr=(e,{cacheSize:o,prefix:r,experimentalParseClassName:t,extend:s={},override:n={}})=>(U(e,"cacheSize",o),U(e,"prefix",r),U(e,"experimentalParseClassName",t),Q(e.theme,n.theme),Q(e.classGroups,n.classGroups),Q(e.conflictingClassGroups,n.conflictingClassGroups),Q(e.conflictingClassGroupModifiers,n.conflictingClassGroupModifiers),U(e,"orderSensitiveModifiers",n.orderSensitiveModifiers),Z(e.theme,s.theme),Z(e.classGroups,s.classGroups),Z(e.conflictingClassGroups,s.conflictingClassGroups),Z(e.conflictingClassGroupModifiers,s.conflictingClassGroupModifiers),je(e,s,"orderSensitiveModifiers"),e),U=(e,o,r)=>{r!==void 0&&(e[o]=r)},Q=(e,o)=>{if(o)for(const r in o)U(e,r,o[r])},Z=(e,o)=>{if(o)for(const r in o)je(e,o,r)},je=(e,o,r)=>{const t=o[r];t!==void 0&&(e[r]=e[r]?e[r].concat(t):t)},Xr=(e,...o)=>typeof e=="function"?ce(de,e,...o):ce(()=>Yr(de(),e),...o),qr=ce(de);function Qr(...e){return qr(Me(e))}export{ye as a,Dr as b,Qr as c,Xr as e,nr as s,qr as t};
