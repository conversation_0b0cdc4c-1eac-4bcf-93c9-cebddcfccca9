import { d as copy_payload, f as assign_payload, c as pop, p as push, g as ensure_array_like, l as attr, e as escape_html, h as attr_class, j as stringify } from "../../../chunks/context.js";
import { A as Arrow_up } from "../../../chunks/arrow-up.js";
import { B as Button } from "../../../chunks/button.js";
import { T as Textarea } from "../../../chunks/textarea.js";
import { S as StreamingMarkdown } from "../../../chunks/StreamingMarkdown.js";
import { C as Chat } from "../../../chunks/chat.svelte.js";
function _page($$payload, $$props) {
  push();
  const chat = new Chat({ api: "/api/chat" });
  let input = "";
  const disabled = chat.status !== "ready";
  function mapRoleToClass(role) {
    return role === "assistant" ? "bg-blue-50 text-blue-900 rounded-lg border border-blue-200" : "bg-gray-50 text-gray-900 rounded-lg border border-gray-200 justify-self-end";
  }
  function handleSubmit(e) {
    e.preventDefault();
    if (input.trim()) {
      chat.sendMessage({ text: input });
      input = "";
    }
  }
  const sampleQuestions = [
    "请用 Markdown 格式介绍一下 Svelte 5 的新特性",
    "写一个关于 JavaScript 异步编程的教程，包含代码示例",
    "创建一个包含表格和列表的项目计划文档",
    "解释什么是响应式编程，并提供示例代码"
  ];
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    const each_array_1 = ensure_array_like(chat.messages);
    $$payload2.out += `<main class="flex flex-col h-screen bg-gray-50"><header class="bg-white border-b border-gray-200 p-4"><div class="max-w-4xl mx-auto"><h1 class="text-2xl font-bold text-gray-900">流式 Markdown AI 聊天</h1> <p class="text-gray-600 mt-1">体验 AI 回复的实时 Markdown 渲染和逐字淡入效果</p></div></header> <div class="flex-1 max-w-4xl mx-auto w-full flex flex-col"><div class="flex-1 overflow-y-auto p-4 space-y-4">`;
    if (chat.messages.length === 0) {
      $$payload2.out += "<!--[-->";
      const each_array = ensure_array_like(sampleQuestions);
      $$payload2.out += `<div class="text-center py-12"><div class="mb-8"><h2 class="text-xl font-semibold text-gray-700 mb-2">开始对话</h2> <p class="text-gray-500">AI 的回复将以流式 Markdown 格式呈现，支持逐字淡入动画</p></div> <div class="grid grid-cols-1 md:grid-cols-2 gap-3 max-w-2xl mx-auto"><!--[-->`;
      for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
        let question = each_array[$$index];
        $$payload2.out += `<button class="p-3 text-left bg-white border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors"${attr("disabled", disabled, true)}><div class="text-sm text-gray-700">${escape_html(question)}</div></button>`;
      }
      $$payload2.out += `<!--]--></div></div>`;
    } else {
      $$payload2.out += "<!--[!-->";
    }
    $$payload2.out += `<!--]--> <!--[-->`;
    for (let $$index_2 = 0, $$length = each_array_1.length; $$index_2 < $$length; $$index_2++) {
      let message = each_array_1[$$index_2];
      const each_array_2 = ensure_array_like(message.parts);
      $$payload2.out += `<div${attr_class(`flex ${stringify(message.role === "assistant" ? "justify-start" : "justify-end")}`)}><div${attr_class(`${stringify(mapRoleToClass(message.role))} max-w-[85%] p-4`)}><div class="flex items-center mb-2"><div${attr_class(`w-6 h-6 rounded-full ${stringify(message.role === "assistant" ? "bg-blue-500" : "bg-gray-500")} flex items-center justify-center text-white text-xs font-bold`)}>${escape_html(message.role === "assistant" ? "AI" : "U")}</div> <span${attr_class(`ml-2 text-xs font-medium ${stringify(message.role === "assistant" ? "text-blue-700" : "text-gray-700")}`)}>${escape_html(message.role === "assistant" ? "AI 助手" : "用户")}</span></div> <!--[-->`;
      for (let i = 0, $$length2 = each_array_2.length; i < $$length2; i++) {
        let part = each_array_2[i];
        if (part.type === "text") {
          $$payload2.out += "<!--[-->";
          if (message.role === "assistant") {
            $$payload2.out += "<!--[-->";
            $$payload2.out += `<div class="prose prose-sm max-w-none">`;
            StreamingMarkdown($$payload2, {
              content: part.text,
              animationDelay: 15,
              fadeInDuration: 300
            });
            $$payload2.out += `<!----></div>`;
          } else {
            $$payload2.out += "<!--[!-->";
            $$payload2.out += `<div class="whitespace-pre-wrap">${escape_html(part.text)}</div>`;
          }
          $$payload2.out += `<!--]-->`;
        } else if (part.type === "tool-askForConfirmation") {
          $$payload2.out += "<!--[1-->";
          const toolCallId = part.toolCallId;
          const state = part.state;
          if (state === "input-available") {
            $$payload2.out += "<!--[-->";
            const input2 = part.input;
            $$payload2.out += `<div class="flex flex-col gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded"><div class="text-yellow-800">${escape_html(input2.message)}</div> <div class="flex gap-2">`;
            Button($$payload2, {
              variant: "default",
              size: "sm",
              onclick: () => chat.addToolResult({
                toolCallId,
                tool: "askForConfirmation",
                output: "Yes, confirmed"
              }),
              children: ($$payload3) => {
                $$payload3.out += `<!---->确认`;
              },
              $$slots: { default: true }
            });
            $$payload2.out += `<!----> `;
            Button($$payload2, {
              variant: "secondary",
              size: "sm",
              onclick: () => chat.addToolResult({
                toolCallId,
                tool: "askForConfirmation",
                output: "No, denied"
              }),
              children: ($$payload3) => {
                $$payload3.out += `<!---->取消`;
              },
              $$slots: { default: true }
            });
            $$payload2.out += `<!----></div></div>`;
          } else if (state === "output-available") {
            $$payload2.out += "<!--[1-->";
            $$payload2.out += `<div class="text-gray-600 text-sm">${escape_html(part.output)}</div>`;
          } else {
            $$payload2.out += "<!--[!-->";
          }
          $$payload2.out += `<!--]-->`;
        } else if (part.type === "tool-getLocation") {
          $$payload2.out += "<!--[2-->";
          if (part.state === "input-available") {
            $$payload2.out += "<!--[-->";
            $$payload2.out += `<div class="text-blue-600 text-sm">正在获取位置信息...</div>`;
          } else if (part.state === "output-available") {
            $$payload2.out += "<!--[1-->";
            $$payload2.out += `<div class="text-blue-600 text-sm">位置: ${escape_html(part.output)}</div>`;
          } else {
            $$payload2.out += "<!--[!-->";
          }
          $$payload2.out += `<!--]-->`;
        } else if (part.type === "tool-getWeatherInformation") {
          $$payload2.out += "<!--[3-->";
          if (part.state === "input-available") {
            $$payload2.out += "<!--[-->";
            const input2 = part.input;
            $$payload2.out += `<div class="text-green-600 text-sm">正在获取 ${escape_html(input2.city)} 的天气信息...</div>`;
          } else if (part.state === "output-available") {
            $$payload2.out += "<!--[1-->";
            const input2 = part.input;
            $$payload2.out += `<div class="text-green-600 text-sm">${escape_html(input2.city)} 的天气: ${escape_html(part.output)}</div>`;
          } else {
            $$payload2.out += "<!--[!-->";
          }
          $$payload2.out += `<!--]-->`;
        } else {
          $$payload2.out += "<!--[!-->";
        }
        $$payload2.out += `<!--]-->`;
      }
      $$payload2.out += `<!--]--></div></div>`;
    }
    $$payload2.out += `<!--]--> `;
    if (chat.status === "loading") {
      $$payload2.out += "<!--[-->";
      $$payload2.out += `<div class="flex justify-start"><div class="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-[85%]"><div class="flex items-center space-x-2"><div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div> <div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style="animation-delay: 0.1s"></div> <div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style="animation-delay: 0.2s"></div> <span class="text-blue-700 text-sm ml-2">AI 正在思考...</span></div></div></div>`;
    } else {
      $$payload2.out += "<!--[!-->";
    }
    $$payload2.out += `<!--]--></div> <div class="border-t border-gray-200 bg-white p-4"><form class="relative max-w-4xl mx-auto">`;
    Textarea($$payload2, {
      placeholder: "输入您的问题... (支持 Shift+Enter 换行)",
      class: "min-h-[60px] pr-12 resize-none",
      onkeydown: (event) => {
        if (event.key === "Enter" && !event.shiftKey) {
          event.preventDefault();
          handleSubmit(event);
        }
      },
      get value() {
        return input;
      },
      set value($$value) {
        input = $$value;
        $$settled = false;
      }
    });
    $$payload2.out += `<!----> `;
    Button($$payload2, {
      "aria-label": "发送消息",
      disabled: disabled || !input.trim(),
      type: "submit",
      size: "icon",
      class: "absolute right-2 bottom-2",
      children: ($$payload3) => {
        Arrow_up($$payload3, {});
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----></form> <div class="text-center mt-2"><span class="text-xs text-gray-500">状态: ${escape_html(chat.status === "ready" ? "就绪" : chat.status === "loading" ? "处理中" : chat.status)}</span></div></div></div></main>`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}
export {
  _page as default
};
