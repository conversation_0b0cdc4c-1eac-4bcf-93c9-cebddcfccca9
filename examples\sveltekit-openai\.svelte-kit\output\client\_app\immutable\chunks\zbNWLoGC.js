import{K as ae,y as re,L as W,M as S,C as x,N as ne,O as ie,g as Y,P as te,Q as fe,R as le,S as U,T as D,D as N,U as se,V as Z,z as $,W as ue,X as M,Y as G,Z as ve,_ as X,$ as b,a0 as F,a1 as y,F as oe,a2 as K,H as de,a3 as _e,a4 as he,a5 as ce,a6 as pe,B as me,a7 as Ee,a8 as Te,t as we}from"./Cc9lkQ6R.js";import{n as Ae,a as ge}from"./CGanT4Ze.js";import{a as P}from"./krzcCheT.js";import{p as xe}from"./0U5e1eCd.js";function Me(t,e){return e}function Ie(t,e,a,l){for(var v=[],d=e.length,u=0;u<d;u++)he(e[u].e,v,!0);var _=d>0&&v.length===0&&a!==null;if(_){var E=a.parentNode;ce(E),E.append(a),l.clear(),g(t,e[0].prev,e[d-1].next)}pe(v,()=>{for(var c=0;c<d;c++){var o=e[c];_||(l.delete(o.k),g(t,o.prev,o.next)),me(o.e,!_)}})}function be(t,e,a,l,v,d=null){var u=t,_={flags:e,items:new Map,first:null},E=(e&W)!==0;if(E){var c=t;u=x?S(ne(c)):c.appendChild(ae())}x&&ie();var o=null,I=!1,i=te(()=>{var s=a();return de(s)?s:s==null?[]:G(s)});re(()=>{var s=Y(i),r=s.length;if(I&&r===0)return;I=r===0;let w=!1;if(x){var p=fe(u)===le;p!==(r===0)&&(u=U(),S(u),D(!1),w=!0)}if(x){for(var m=null,T,h=0;h<r;h++){if(N.nodeType===8&&N.data===se){u=N,w=!0,D(!1);break}var n=s[h],f=l(n,h);T=J(N,_,m,null,n,f,h,v,e,a),_.items.set(f,T),m=T}r>0&&S(U())}x||Ce(s,_,u,v,e,l,a),d!==null&&(r===0?o?Z(o):o=$(()=>d(u)):o!==null&&ue(o,()=>{o=null})),w&&D(!0),Y(i)}),x&&(u=N)}function Ce(t,e,a,l,v,d,u){var k,O,z,V;var _=(v&_e)!==0,E=(v&(b|y))!==0,c=t.length,o=e.items,I=e.first,i=I,s,r=null,w,p=[],m=[],T,h,n,f;if(_)for(f=0;f<c;f+=1)T=t[f],h=d(T,f),n=o.get(h),n!==void 0&&((k=n.a)==null||k.measure(),(w??(w=new Set)).add(n));for(f=0;f<c;f+=1){if(T=t[f],h=d(T,f),n=o.get(h),n===void 0){var j=i?i.e.nodes_start:a;r=J(j,e,r,r===null?e.first:r.next,T,h,f,l,v,u),o.set(h,r),p=[],m=[],i=r.next;continue}if(E&&Ne(n,T,f,v),(n.e.f&M)!==0&&(Z(n.e),_&&((O=n.a)==null||O.unfix(),(w??(w=new Set)).delete(n))),n!==i){if(s!==void 0&&s.has(n)){if(p.length<m.length){var q=m[0],A;r=q.prev;var L=p[0],H=p[p.length-1];for(A=0;A<p.length;A+=1)Q(p[A],q,a);for(A=0;A<m.length;A+=1)s.delete(m[A]);g(e,L.prev,H.next),g(e,r,L),g(e,H,q),i=q,r=H,f-=1,p=[],m=[]}else s.delete(n),Q(n,i,a),g(e,n.prev,n.next),g(e,n,r===null?e.first:r.next),g(e,r,n),r=n;continue}for(p=[],m=[];i!==null&&i.k!==h;)(i.e.f&M)===0&&(s??(s=new Set)).add(i),m.push(i),i=i.next;if(i===null)continue;n=i}p.push(n),r=n,i=n.next}if(i!==null||s!==void 0){for(var C=s===void 0?[]:G(s);i!==null;)(i.e.f&M)===0&&C.push(i),i=i.next;var R=C.length;if(R>0){var ee=(v&W)!==0&&c===0?a:null;if(_){for(f=0;f<R;f+=1)(z=C[f].a)==null||z.measure();for(f=0;f<R;f+=1)(V=C[f].a)==null||V.fix()}Ie(e,C,ee,o)}}_&&ve(()=>{var B;if(w!==void 0)for(n of w)(B=n.a)==null||B.apply()}),X.first=e.first&&e.first.e,X.last=r&&r.e}function Ne(t,e,a,l){(l&b)!==0&&F(t.v,e),(l&y)!==0?F(t.i,a):t.i=a}function J(t,e,a,l,v,d,u,_,E,c){var o=(E&b)!==0,I=(E&Ee)===0,i=o?I?oe(v):K(v):v,s=(E&y)===0?u:K(u),r={i:s,v:i,k:d,a:null,e:null,prev:a,next:l};try{return r.e=$(()=>_(t,i,s,c),x),r.e.prev=a&&a.e,r.e.next=l&&l.e,a===null?e.first=r:(a.next=r,a.e.next=r.e),l!==null&&(l.prev=r,l.e.prev=r.e),r}finally{}}function Q(t,e,a){for(var l=t.next?t.next.e.nodes_start:a,v=e?e.e.nodes_start:a,d=t.e.nodes_start;d!==l;){var u=Te(d);v.before(d),d=u}}function g(t,e,a){e===null?t.first=a:(e.next=a,e.e.next=a&&a.e),a!==null&&(a.prev=e,a.e.prev=e&&e.e)}var qe=Ae('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="currentColor" d="m11 7.825l-4.9 4.9q-.3.3-.7.288t-.7-.313q-.275-.3-.288-.7t.288-.7l6.6-6.6q.15-.15.325-.212T12 4.425t.375.063t.325.212l6.6 6.6q.275.275.275.688t-.275.712q-.3.3-.712.3t-.713-.3L13 7.825V19q0 .425-.288.713T12 20t-.712-.288T11 19z"></path></svg>');function ye(t,e){let a=xe(e,"size",3,16);var l=qe();we(()=>{P(l,"width",a()),P(l,"height",a())}),ge(t,l)}export{ye as A,be as e,Me as i};
