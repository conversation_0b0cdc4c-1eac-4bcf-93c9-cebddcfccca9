import "clsx";
import { c as pop, p as push } from "../../chunks/context.js";
import { K as KeyedCompletionStore, s as setCompletionContext } from "../../chunks/completion-context.svelte.js";
import { K as KeyedStructuredObjectStore, s as setStructuredObjectContext } from "../../chunks/structured-object-context.svelte.js";
function createAIContext() {
  const completionStore = new KeyedCompletionStore();
  setCompletionContext(completionStore);
  const objectStore = new KeyedStructuredObjectStore();
  setStructuredObjectContext(objectStore);
}
function _layout($$payload, $$props) {
  push();
  let { children } = $$props;
  createAIContext();
  children($$payload);
  $$payload.out += `<!---->`;
  pop();
}
export {
  _layout as default
};
