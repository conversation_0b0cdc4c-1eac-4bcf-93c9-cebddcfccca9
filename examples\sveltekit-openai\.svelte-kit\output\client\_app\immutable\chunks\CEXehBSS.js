import{t as Fn,a as Vn}from"./CGanT4Ze.js";import{aK as Bn,l as Jn,af as Wn,C as qn,p as Yn,t as Kn,a as Gn}from"./Cc9lkQ6R.js";import{l as Hn,r as Xn}from"./2Hh1nz_V.js";import{c as Qn,b as ea}from"./krzcCheT.js";import{p as Ut,b as ta,r as ra}from"./0U5e1eCd.js";function na(t,e,r=e){var n=Bn();Hn(t,"input",a=>{var s=a?t.defaultValue:t.value;if(s=pt(t)?ft(s):s,r(s),n&&s!==(s=e())){var o=t.selectionStart,i=t.selectionEnd;t.value=s??"",i!==null&&(t.selectionStart=o,t.selectionEnd=Math.min(i,t.value.length))}}),(qn&&t.defaultValue!==t.value||Jn(e)==null&&t.value)&&r(pt(t)?ft(t.value):t.value),Wn(()=>{var a=e();pt(t)&&a===ft(t.value)||t.type==="date"&&!a&&!t.value||a!==t.value&&(t.value=a??"")})}function pt(t){var e=t.type;return e==="number"||e==="range"}function ft(t){return t===""?null:+t}var Ar="vercel.ai.error",aa=Symbol.for(Ar),Nr,sa=class Cr extends Error{constructor({name:e,message:r,cause:n}){super(r),this[Nr]=!0,this.name=e,this.cause=n}static isInstance(e){return Cr.hasMarker(e,Ar)}static hasMarker(e,r){const n=Symbol.for(r);return e!=null&&typeof e=="object"&&n in e&&typeof e[n]=="boolean"&&e[n]===!0}};Nr=aa;var pe=sa;function Rr(t){return t==null?"unknown error":typeof t=="string"?t:t instanceof Error?t.message:JSON.stringify(t)}var Pr="AI_InvalidArgumentError",Mr=`vercel.ai.error.${Pr}`,ia=Symbol.for(Mr),jr,oa=class extends pe{constructor({message:t,cause:e,argument:r}){super({name:Pr,message:t,cause:e}),this[jr]=!0,this.argument=r}static isInstance(t){return pe.hasMarker(t,Mr)}};jr=ia;var Dr="AI_JSONParseError",Lr=`vercel.ai.error.${Dr}`,ua=Symbol.for(Lr),Ur,Ft=class extends pe{constructor({text:t,cause:e}){super({name:Dr,message:`JSON parsing failed: Text: ${t}.
Error message: ${Rr(e)}`,cause:e}),this[Ur]=!0,this.text=t}static isInstance(t){return pe.hasMarker(t,Lr)}};Ur=ua;var Fr="AI_TypeValidationError",Vr=`vercel.ai.error.${Fr}`,ca=Symbol.for(Vr),Br,la=class bt extends pe{constructor({value:e,cause:r}){super({name:Fr,message:`Type validation failed: Value: ${JSON.stringify(e)}.
Error message: ${Rr(r)}`,cause:r}),this[Br]=!0,this.value=e}static isInstance(e){return pe.hasMarker(e,Vr)}static wrap({value:e,cause:r}){return bt.isInstance(r)&&r.value===e?r:new bt({value:e,cause:r})}};Br=ca;var Ke=la;class Vt extends Error{constructor(e,r){super(e),this.name="ParseError",this.type=r.type,this.field=r.field,this.value=r.value,this.line=r.line}}function ht(t){}function da(t){if(typeof t=="function")throw new TypeError("`callbacks` must be an object, got a function instead. Did you mean `{onEvent: fn}`?");const{onEvent:e=ht,onError:r=ht,onRetry:n=ht,onComment:a}=t;let s="",o=!0,i,c="",u="";function d(Z){const g=o?Z.replace(/^\xEF\xBB\xBF/,""):Z,[l,h]=pa(`${s}${g}`);for(const v of l)y(v);s=h,o=!1}function y(Z){if(Z===""){O();return}if(Z.startsWith(":")){a&&a(Z.slice(Z.startsWith(": ")?2:1));return}const g=Z.indexOf(":");if(g!==-1){const l=Z.slice(0,g),h=Z[g+1]===" "?2:1,v=Z.slice(g+h);x(l,v,Z);return}x(Z,"",Z)}function x(Z,g,l){switch(Z){case"event":u=g;break;case"data":c=`${c}${g}
`;break;case"id":i=g.includes("\0")?void 0:g;break;case"retry":/^\d+$/.test(g)?n(parseInt(g,10)):r(new Vt(`Invalid \`retry\` value: "${g}"`,{type:"invalid-retry",value:g,line:l}));break;default:r(new Vt(`Unknown field "${Z.length>20?`${Z.slice(0,20)}…`:Z}"`,{type:"unknown-field",field:Z,value:g,line:l}));break}}function O(){c.length>0&&e({id:i,event:u||void 0,data:c.endsWith(`
`)?c.slice(0,-1):c}),i=void 0,c="",u=""}function w(Z={}){s&&Z.consume&&y(s),o=!0,i=void 0,c="",u="",s=""}return{feed:d,reset:w}}function pa(t){const e=[];let r="",n=0;for(;n<t.length;){const a=t.indexOf("\r",n),s=t.indexOf(`
`,n);let o=-1;if(a!==-1&&s!==-1?o=Math.min(a,s):a!==-1?o=a:s!==-1&&(o=s),o===-1){r=t.slice(n);break}else{const i=t.slice(n,o);e.push(i),n=o+1,t[n-1]==="\r"&&t[n]===`
`&&n++}}return[e,r]}class fa extends TransformStream{constructor({onError:e,onRetry:r,onComment:n}={}){let a;super({start(s){a=da({onEvent:o=>{s.enqueue(o)},onError(o){e==="terminate"?s.error(o):typeof e=="function"&&e(o)},onRetry:r,onComment:n})},transform(s){a.feed(s)}})}}function f(t,e,r){function n(i,c){var u;Object.defineProperty(i,"_zod",{value:i._zod??{},enumerable:!1}),(u=i._zod).traits??(u.traits=new Set),i._zod.traits.add(t),e(i,c);for(const d in o.prototype)d in i||Object.defineProperty(i,d,{value:o.prototype[d].bind(i)});i._zod.constr=o,i._zod.def=c}const a=(r==null?void 0:r.Parent)??Object;class s extends a{}Object.defineProperty(s,"name",{value:t});function o(i){var c;const u=r!=null&&r.Parent?new s:this;n(u,i),(c=u._zod).deferred??(c.deferred=[]);for(const d of u._zod.deferred)d();return u}return Object.defineProperty(o,"init",{value:n}),Object.defineProperty(o,Symbol.hasInstance,{value:i=>{var c,u;return r!=null&&r.Parent&&i instanceof r.Parent?!0:(u=(c=i==null?void 0:i._zod)==null?void 0:c.traits)==null?void 0:u.has(t)}}),Object.defineProperty(o,"name",{value:t}),o}class Ce extends Error{constructor(){super("Encountered Promise during synchronous parse. Use .parseAsync() instead.")}}const Jr={};function fe(t){return Jr}function Wr(t){const e=Object.values(t).filter(n=>typeof n=="number");return Object.entries(t).filter(([n,a])=>e.indexOf(+n)===-1).map(([n,a])=>a)}function ha(t,e){return typeof e=="bigint"?e.toString():e}function Ot(t){return{get value(){{const e=t();return Object.defineProperty(this,"value",{value:e}),e}}}}function zt(t){return t==null}function At(t){const e=t.startsWith("^")?1:0,r=t.endsWith("$")?t.length-1:t.length;return t.slice(e,r)}function ma(t,e){const r=(t.toString().split(".")[1]||"").length,n=(e.toString().split(".")[1]||"").length,a=r>n?r:n,s=Number.parseInt(t.toFixed(a).replace(".","")),o=Number.parseInt(e.toFixed(a).replace(".",""));return s%o/10**a}function L(t,e,r){Object.defineProperty(t,e,{get(){{const n=r();return t[e]=n,n}},set(n){Object.defineProperty(t,e,{value:n})},configurable:!0})}function Fe(t,e,r){Object.defineProperty(t,e,{value:r,writable:!0,enumerable:!0,configurable:!0})}function $e(t){return JSON.stringify(t)}const qr=Error.captureStackTrace?Error.captureStackTrace:(...t)=>{};function Ge(t){return typeof t=="object"&&t!==null&&!Array.isArray(t)}const ga=Ot(()=>{var t;if(typeof navigator<"u"&&((t=navigator==null?void 0:navigator.userAgent)!=null&&t.includes("Cloudflare")))return!1;try{const e=Function;return new e(""),!0}catch{return!1}});function He(t){if(Ge(t)===!1)return!1;const e=t.constructor;if(e===void 0)return!0;const r=e.prototype;return!(Ge(r)===!1||Object.prototype.hasOwnProperty.call(r,"isPrototypeOf")===!1)}const va=new Set(["string","number","symbol"]);function Ve(t){return t.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function we(t,e,r){const n=new t._zod.constr(e??t._zod.def);return(!e||r!=null&&r.parent)&&(n._zod.parent=t),n}function T(t){const e=t;if(!e)return{};if(typeof e=="string")return{error:()=>e};if((e==null?void 0:e.message)!==void 0){if((e==null?void 0:e.error)!==void 0)throw new Error("Cannot specify both `message` and `error` params");e.error=e.message}return delete e.message,typeof e.error=="string"?{...e,error:()=>e.error}:e}function ya(t){return Object.keys(t).filter(e=>t[e]._zod.optin==="optional"&&t[e]._zod.optout==="optional")}const _a={safeint:[Number.MIN_SAFE_INTEGER,Number.MAX_SAFE_INTEGER],int32:[-2147483648,2147483647],uint32:[0,4294967295],float32:[-34028234663852886e22,34028234663852886e22],float64:[-Number.MAX_VALUE,Number.MAX_VALUE]};function ba(t,e){const r={},n=t._zod.def;for(const a in e){if(!(a in n.shape))throw new Error(`Unrecognized key: "${a}"`);e[a]&&(r[a]=n.shape[a])}return we(t,{...t._zod.def,shape:r,checks:[]})}function wa(t,e){const r={...t._zod.def.shape},n=t._zod.def;for(const a in e){if(!(a in n.shape))throw new Error(`Unrecognized key: "${a}"`);e[a]&&delete r[a]}return we(t,{...t._zod.def,shape:r,checks:[]})}function ka(t,e){if(!He(e))throw new Error("Invalid input to extend: expected a plain object");const r={...t._zod.def,get shape(){const n={...t._zod.def.shape,...e};return Fe(this,"shape",n),n},checks:[]};return we(t,r)}function xa(t,e){return we(t,{...t._zod.def,get shape(){const r={...t._zod.def.shape,...e._zod.def.shape};return Fe(this,"shape",r),r},catchall:e._zod.def.catchall,checks:[]})}function Ia(t,e,r){const n=e._zod.def.shape,a={...n};if(r)for(const s in r){if(!(s in n))throw new Error(`Unrecognized key: "${s}"`);r[s]&&(a[s]=t?new t({type:"optional",innerType:n[s]}):n[s])}else for(const s in n)a[s]=t?new t({type:"optional",innerType:n[s]}):n[s];return we(e,{...e._zod.def,shape:a,checks:[]})}function Sa(t,e,r){const n=e._zod.def.shape,a={...n};if(r)for(const s in r){if(!(s in a))throw new Error(`Unrecognized key: "${s}"`);r[s]&&(a[s]=new t({type:"nonoptional",innerType:n[s]}))}else for(const s in n)a[s]=new t({type:"nonoptional",innerType:n[s]});return we(e,{...e._zod.def,shape:a,checks:[]})}function Ae(t,e=0){var r;for(let n=e;n<t.issues.length;n++)if(((r=t.issues[n])==null?void 0:r.continue)!==!0)return!0;return!1}function ye(t,e){return e.map(r=>{var n;return(n=r).path??(n.path=[]),r.path.unshift(t),r})}function We(t){return typeof t=="string"?t:t==null?void 0:t.message}function he(t,e,r){var a,s,o,i,c,u;const n={...t,path:t.path??[]};if(!t.message){const d=We((o=(s=(a=t.inst)==null?void 0:a._zod.def)==null?void 0:s.error)==null?void 0:o.call(s,t))??We((i=e==null?void 0:e.error)==null?void 0:i.call(e,t))??We((c=r.customError)==null?void 0:c.call(r,t))??We((u=r.localeError)==null?void 0:u.call(r,t))??"Invalid input";n.message=d}return delete n.inst,delete n.continue,e!=null&&e.reportInput||delete n.input,n}function Nt(t){return Array.isArray(t)?"array":typeof t=="string"?"string":"unknown"}function Re(...t){const[e,r,n]=t;return typeof e=="string"?{message:e,code:"custom",input:r,inst:n}:{...e}}const Yr=(t,e)=>{t.name="$ZodError",Object.defineProperty(t,"_zod",{value:t._zod,enumerable:!1}),Object.defineProperty(t,"issues",{value:e,enumerable:!1}),Object.defineProperty(t,"message",{get(){return JSON.stringify(e,ha,2)},enumerable:!0}),Object.defineProperty(t,"toString",{value:()=>t.message,enumerable:!1})},Kr=f("$ZodError",Yr),Gr=f("$ZodError",Yr,{Parent:Error});function Ta(t,e=r=>r.message){const r={},n=[];for(const a of t.issues)a.path.length>0?(r[a.path[0]]=r[a.path[0]]||[],r[a.path[0]].push(e(a))):n.push(e(a));return{formErrors:n,fieldErrors:r}}function Ea(t,e){const r=e||function(s){return s.message},n={_errors:[]},a=s=>{for(const o of s.issues)if(o.code==="invalid_union"&&o.errors.length)o.errors.map(i=>a({issues:i}));else if(o.code==="invalid_key")a({issues:o.issues});else if(o.code==="invalid_element")a({issues:o.issues});else if(o.path.length===0)n._errors.push(r(o));else{let i=n,c=0;for(;c<o.path.length;){const u=o.path[c];c===o.path.length-1?(i[u]=i[u]||{_errors:[]},i[u]._errors.push(r(o))):i[u]=i[u]||{_errors:[]},i=i[u],c++}}};return a(t),n}const Za=t=>(e,r,n,a)=>{const s=n?Object.assign(n,{async:!1}):{async:!1},o=e._zod.run({value:r,issues:[]},s);if(o instanceof Promise)throw new Ce;if(o.issues.length){const i=new((a==null?void 0:a.Err)??t)(o.issues.map(c=>he(c,s,fe())));throw qr(i,a==null?void 0:a.callee),i}return o.value},$a=t=>async(e,r,n,a)=>{const s=n?Object.assign(n,{async:!0}):{async:!0};let o=e._zod.run({value:r,issues:[]},s);if(o instanceof Promise&&(o=await o),o.issues.length){const i=new((a==null?void 0:a.Err)??t)(o.issues.map(c=>he(c,s,fe())));throw qr(i,a==null?void 0:a.callee),i}return o.value},Hr=t=>(e,r,n)=>{const a=n?{...n,async:!1}:{async:!1},s=e._zod.run({value:r,issues:[]},a);if(s instanceof Promise)throw new Ce;return s.issues.length?{success:!1,error:new(t??Kr)(s.issues.map(o=>he(o,a,fe())))}:{success:!0,data:s.value}},Oa=Hr(Gr),Xr=t=>async(e,r,n)=>{const a=n?Object.assign(n,{async:!0}):{async:!0};let s=e._zod.run({value:r,issues:[]},a);return s instanceof Promise&&(s=await s),s.issues.length?{success:!1,error:new t(s.issues.map(o=>he(o,a,fe())))}:{success:!0,data:s.value}},za=Xr(Gr),Aa=/^[cC][^\s-]{8,}$/,Na=/^[0-9a-z]+$/,Ca=/^[0-9A-HJKMNP-TV-Za-hjkmnp-tv-z]{26}$/,Ra=/^[0-9a-vA-V]{20}$/,Pa=/^[A-Za-z0-9]{27}$/,Ma=/^[a-zA-Z0-9_-]{21}$/,ja=/^P(?:(\d+W)|(?!.*W)(?=\d|T\d)(\d+Y)?(\d+M)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+([.,]\d+)?S)?)?)$/,Da=/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})$/,Bt=t=>t?new RegExp(`^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-${t}[0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12})$`):/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-8][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}|00000000-0000-0000-0000-000000000000)$/,La=/^(?!\.)(?!.*\.\.)([A-Za-z0-9_'+\-\.]*)[A-Za-z0-9_+-]@([A-Za-z0-9][A-Za-z0-9\-]*\.)+[A-Za-z]{2,}$/,Ua="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";function Fa(){return new RegExp(Ua,"u")}const Va=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,Ba=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})$/,Ja=/^((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/([0-9]|[1-2][0-9]|3[0-2])$/,Wa=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,qa=/^$|^(?:[0-9a-zA-Z+/]{4})*(?:(?:[0-9a-zA-Z+/]{2}==)|(?:[0-9a-zA-Z+/]{3}=))?$/,Qr=/^[A-Za-z0-9_-]*$/,Ya=/^([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+$/,Ka=/^\+(?:[0-9]){6,14}[0-9]$/,en="(?:(?:\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-(?:(?:0[13578]|1[02])-(?:0[1-9]|[12]\\d|3[01])|(?:0[469]|11)-(?:0[1-9]|[12]\\d|30)|(?:02)-(?:0[1-9]|1\\d|2[0-8])))",Ga=new RegExp(`^${en}$`);function tn(t){const e="(?:[01]\\d|2[0-3]):[0-5]\\d";return typeof t.precision=="number"?t.precision===-1?`${e}`:t.precision===0?`${e}:[0-5]\\d`:`${e}:[0-5]\\d\\.\\d{${t.precision}}`:`${e}(?::[0-5]\\d(?:\\.\\d+)?)?`}function Ha(t){return new RegExp(`^${tn(t)}$`)}function Xa(t){const e=tn({precision:t.precision}),r=["Z"];t.local&&r.push(""),t.offset&&r.push("([+-]\\d{2}:\\d{2})");const n=`${e}(?:${r.join("|")})`;return new RegExp(`^${en}T(?:${n})$`)}const Qa=t=>{const e=t?`[\\s\\S]{${(t==null?void 0:t.minimum)??0},${(t==null?void 0:t.maximum)??""}}`:"[\\s\\S]*";return new RegExp(`^${e}$`)},es=/^\d+$/,ts=/^-?\d+(?:\.\d+)?/i,rs=/true|false/i,ns=/null/i,as=/^[^A-Z]*$/,ss=/^[^a-z]*$/,re=f("$ZodCheck",(t,e)=>{var r;t._zod??(t._zod={}),t._zod.def=e,(r=t._zod).onattach??(r.onattach=[])}),rn={number:"number",bigint:"bigint",object:"date"},nn=f("$ZodCheckLessThan",(t,e)=>{re.init(t,e);const r=rn[typeof e.value];t._zod.onattach.push(n=>{const a=n._zod.bag,s=(e.inclusive?a.maximum:a.exclusiveMaximum)??Number.POSITIVE_INFINITY;e.value<s&&(e.inclusive?a.maximum=e.value:a.exclusiveMaximum=e.value)}),t._zod.check=n=>{(e.inclusive?n.value<=e.value:n.value<e.value)||n.issues.push({origin:r,code:"too_big",maximum:e.value,input:n.value,inclusive:e.inclusive,inst:t,continue:!e.abort})}}),an=f("$ZodCheckGreaterThan",(t,e)=>{re.init(t,e);const r=rn[typeof e.value];t._zod.onattach.push(n=>{const a=n._zod.bag,s=(e.inclusive?a.minimum:a.exclusiveMinimum)??Number.NEGATIVE_INFINITY;e.value>s&&(e.inclusive?a.minimum=e.value:a.exclusiveMinimum=e.value)}),t._zod.check=n=>{(e.inclusive?n.value>=e.value:n.value>e.value)||n.issues.push({origin:r,code:"too_small",minimum:e.value,input:n.value,inclusive:e.inclusive,inst:t,continue:!e.abort})}}),is=f("$ZodCheckMultipleOf",(t,e)=>{re.init(t,e),t._zod.onattach.push(r=>{var n;(n=r._zod.bag).multipleOf??(n.multipleOf=e.value)}),t._zod.check=r=>{if(typeof r.value!=typeof e.value)throw new Error("Cannot mix number and bigint in multiple_of check.");(typeof r.value=="bigint"?r.value%e.value===BigInt(0):ma(r.value,e.value)===0)||r.issues.push({origin:typeof r.value,code:"not_multiple_of",divisor:e.value,input:r.value,inst:t,continue:!e.abort})}}),os=f("$ZodCheckNumberFormat",(t,e)=>{var o;re.init(t,e),e.format=e.format||"float64";const r=(o=e.format)==null?void 0:o.includes("int"),n=r?"int":"number",[a,s]=_a[e.format];t._zod.onattach.push(i=>{const c=i._zod.bag;c.format=e.format,c.minimum=a,c.maximum=s,r&&(c.pattern=es)}),t._zod.check=i=>{const c=i.value;if(r){if(!Number.isInteger(c)){i.issues.push({expected:n,format:e.format,code:"invalid_type",input:c,inst:t});return}if(!Number.isSafeInteger(c)){c>0?i.issues.push({input:c,code:"too_big",maximum:Number.MAX_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:t,origin:n,continue:!e.abort}):i.issues.push({input:c,code:"too_small",minimum:Number.MIN_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:t,origin:n,continue:!e.abort});return}}c<a&&i.issues.push({origin:"number",input:c,code:"too_small",minimum:a,inclusive:!0,inst:t,continue:!e.abort}),c>s&&i.issues.push({origin:"number",input:c,code:"too_big",maximum:s,inst:t})}}),us=f("$ZodCheckMaxLength",(t,e)=>{var r;re.init(t,e),(r=t._zod.def).when??(r.when=n=>{const a=n.value;return!zt(a)&&a.length!==void 0}),t._zod.onattach.push(n=>{const a=n._zod.bag.maximum??Number.POSITIVE_INFINITY;e.maximum<a&&(n._zod.bag.maximum=e.maximum)}),t._zod.check=n=>{const a=n.value;if(a.length<=e.maximum)return;const o=Nt(a);n.issues.push({origin:o,code:"too_big",maximum:e.maximum,inclusive:!0,input:a,inst:t,continue:!e.abort})}}),cs=f("$ZodCheckMinLength",(t,e)=>{var r;re.init(t,e),(r=t._zod.def).when??(r.when=n=>{const a=n.value;return!zt(a)&&a.length!==void 0}),t._zod.onattach.push(n=>{const a=n._zod.bag.minimum??Number.NEGATIVE_INFINITY;e.minimum>a&&(n._zod.bag.minimum=e.minimum)}),t._zod.check=n=>{const a=n.value;if(a.length>=e.minimum)return;const o=Nt(a);n.issues.push({origin:o,code:"too_small",minimum:e.minimum,inclusive:!0,input:a,inst:t,continue:!e.abort})}}),ls=f("$ZodCheckLengthEquals",(t,e)=>{var r;re.init(t,e),(r=t._zod.def).when??(r.when=n=>{const a=n.value;return!zt(a)&&a.length!==void 0}),t._zod.onattach.push(n=>{const a=n._zod.bag;a.minimum=e.length,a.maximum=e.length,a.length=e.length}),t._zod.check=n=>{const a=n.value,s=a.length;if(s===e.length)return;const o=Nt(a),i=s>e.length;n.issues.push({origin:o,...i?{code:"too_big",maximum:e.length}:{code:"too_small",minimum:e.length},inclusive:!0,exact:!0,input:n.value,inst:t,continue:!e.abort})}}),ut=f("$ZodCheckStringFormat",(t,e)=>{var r,n;re.init(t,e),t._zod.onattach.push(a=>{const s=a._zod.bag;s.format=e.format,e.pattern&&(s.patterns??(s.patterns=new Set),s.patterns.add(e.pattern))}),e.pattern?(r=t._zod).check??(r.check=a=>{e.pattern.lastIndex=0,!e.pattern.test(a.value)&&a.issues.push({origin:"string",code:"invalid_format",format:e.format,input:a.value,...e.pattern?{pattern:e.pattern.toString()}:{},inst:t,continue:!e.abort})}):(n=t._zod).check??(n.check=()=>{})}),ds=f("$ZodCheckRegex",(t,e)=>{ut.init(t,e),t._zod.check=r=>{e.pattern.lastIndex=0,!e.pattern.test(r.value)&&r.issues.push({origin:"string",code:"invalid_format",format:"regex",input:r.value,pattern:e.pattern.toString(),inst:t,continue:!e.abort})}}),ps=f("$ZodCheckLowerCase",(t,e)=>{e.pattern??(e.pattern=as),ut.init(t,e)}),fs=f("$ZodCheckUpperCase",(t,e)=>{e.pattern??(e.pattern=ss),ut.init(t,e)}),hs=f("$ZodCheckIncludes",(t,e)=>{re.init(t,e);const r=Ve(e.includes),n=new RegExp(typeof e.position=="number"?`^.{${e.position}}${r}`:r);e.pattern=n,t._zod.onattach.push(a=>{const s=a._zod.bag;s.patterns??(s.patterns=new Set),s.patterns.add(n)}),t._zod.check=a=>{a.value.includes(e.includes,e.position)||a.issues.push({origin:"string",code:"invalid_format",format:"includes",includes:e.includes,input:a.value,inst:t,continue:!e.abort})}}),ms=f("$ZodCheckStartsWith",(t,e)=>{re.init(t,e);const r=new RegExp(`^${Ve(e.prefix)}.*`);e.pattern??(e.pattern=r),t._zod.onattach.push(n=>{const a=n._zod.bag;a.patterns??(a.patterns=new Set),a.patterns.add(r)}),t._zod.check=n=>{n.value.startsWith(e.prefix)||n.issues.push({origin:"string",code:"invalid_format",format:"starts_with",prefix:e.prefix,input:n.value,inst:t,continue:!e.abort})}}),gs=f("$ZodCheckEndsWith",(t,e)=>{re.init(t,e);const r=new RegExp(`.*${Ve(e.suffix)}$`);e.pattern??(e.pattern=r),t._zod.onattach.push(n=>{const a=n._zod.bag;a.patterns??(a.patterns=new Set),a.patterns.add(r)}),t._zod.check=n=>{n.value.endsWith(e.suffix)||n.issues.push({origin:"string",code:"invalid_format",format:"ends_with",suffix:e.suffix,input:n.value,inst:t,continue:!e.abort})}}),vs=f("$ZodCheckOverwrite",(t,e)=>{re.init(t,e),t._zod.check=r=>{r.value=e.tx(r.value)}});class ys{constructor(e=[]){this.content=[],this.indent=0,this&&(this.args=e)}indented(e){this.indent+=1,e(this),this.indent-=1}write(e){if(typeof e=="function"){e(this,{execution:"sync"}),e(this,{execution:"async"});return}const n=e.split(`
`).filter(o=>o),a=Math.min(...n.map(o=>o.length-o.trimStart().length)),s=n.map(o=>o.slice(a)).map(o=>" ".repeat(this.indent*2)+o);for(const o of s)this.content.push(o)}compile(){const e=Function,r=this==null?void 0:this.args,a=[...((this==null?void 0:this.content)??[""]).map(s=>`  ${s}`)];return new e(...r,a.join(`
`))}}const _s={major:4,minor:0,patch:0},F=f("$ZodType",(t,e)=>{var a;var r;t??(t={}),t._zod.def=e,t._zod.bag=t._zod.bag||{},t._zod.version=_s;const n=[...t._zod.def.checks??[]];t._zod.traits.has("$ZodCheck")&&n.unshift(t);for(const s of n)for(const o of s._zod.onattach)o(t);if(n.length===0)(r=t._zod).deferred??(r.deferred=[]),(a=t._zod.deferred)==null||a.push(()=>{t._zod.run=t._zod.parse});else{const s=(o,i,c)=>{let u=Ae(o),d;for(const y of i){if(y._zod.def.when){if(!y._zod.def.when(o))continue}else if(u)continue;const x=o.issues.length,O=y._zod.check(o);if(O instanceof Promise&&(c==null?void 0:c.async)===!1)throw new Ce;if(d||O instanceof Promise)d=(d??Promise.resolve()).then(async()=>{await O,o.issues.length!==x&&(u||(u=Ae(o,x)))});else{if(o.issues.length===x)continue;u||(u=Ae(o,x))}}return d?d.then(()=>o):o};t._zod.run=(o,i)=>{const c=t._zod.parse(o,i);if(c instanceof Promise){if(i.async===!1)throw new Ce;return c.then(u=>s(u,n,i))}return s(c,n,i)}}t["~standard"]={validate:s=>{var o;try{const i=Oa(t,s);return i.success?{value:i.data}:{issues:(o=i.error)==null?void 0:o.issues}}catch{return za(t,s).then(c=>{var u;return c.success?{value:c.data}:{issues:(u=c.error)==null?void 0:u.issues}})}},vendor:"zod",version:1}}),Ct=f("$ZodString",(t,e)=>{var r;F.init(t,e),t._zod.pattern=[...((r=t==null?void 0:t._zod.bag)==null?void 0:r.patterns)??[]].pop()??Qa(t._zod.bag),t._zod.parse=(n,a)=>{if(e.coerce)try{n.value=String(n.value)}catch{}return typeof n.value=="string"||n.issues.push({expected:"string",code:"invalid_type",input:n.value,inst:t}),n}}),J=f("$ZodStringFormat",(t,e)=>{ut.init(t,e),Ct.init(t,e)}),bs=f("$ZodGUID",(t,e)=>{e.pattern??(e.pattern=Da),J.init(t,e)}),ws=f("$ZodUUID",(t,e)=>{if(e.version){const n={v1:1,v2:2,v3:3,v4:4,v5:5,v6:6,v7:7,v8:8}[e.version];if(n===void 0)throw new Error(`Invalid UUID version: "${e.version}"`);e.pattern??(e.pattern=Bt(n))}else e.pattern??(e.pattern=Bt());J.init(t,e)}),ks=f("$ZodEmail",(t,e)=>{e.pattern??(e.pattern=La),J.init(t,e)}),xs=f("$ZodURL",(t,e)=>{J.init(t,e),t._zod.check=r=>{try{const n=r.value,a=new URL(n),s=a.href;e.hostname&&(e.hostname.lastIndex=0,e.hostname.test(a.hostname)||r.issues.push({code:"invalid_format",format:"url",note:"Invalid hostname",pattern:Ya.source,input:r.value,inst:t,continue:!e.abort})),e.protocol&&(e.protocol.lastIndex=0,e.protocol.test(a.protocol.endsWith(":")?a.protocol.slice(0,-1):a.protocol)||r.issues.push({code:"invalid_format",format:"url",note:"Invalid protocol",pattern:e.protocol.source,input:r.value,inst:t,continue:!e.abort})),!n.endsWith("/")&&s.endsWith("/")?r.value=s.slice(0,-1):r.value=s;return}catch{r.issues.push({code:"invalid_format",format:"url",input:r.value,inst:t,continue:!e.abort})}}}),Is=f("$ZodEmoji",(t,e)=>{e.pattern??(e.pattern=Fa()),J.init(t,e)}),Ss=f("$ZodNanoID",(t,e)=>{e.pattern??(e.pattern=Ma),J.init(t,e)}),Ts=f("$ZodCUID",(t,e)=>{e.pattern??(e.pattern=Aa),J.init(t,e)}),Es=f("$ZodCUID2",(t,e)=>{e.pattern??(e.pattern=Na),J.init(t,e)}),Zs=f("$ZodULID",(t,e)=>{e.pattern??(e.pattern=Ca),J.init(t,e)}),$s=f("$ZodXID",(t,e)=>{e.pattern??(e.pattern=Ra),J.init(t,e)}),Os=f("$ZodKSUID",(t,e)=>{e.pattern??(e.pattern=Pa),J.init(t,e)}),zs=f("$ZodISODateTime",(t,e)=>{e.pattern??(e.pattern=Xa(e)),J.init(t,e)}),As=f("$ZodISODate",(t,e)=>{e.pattern??(e.pattern=Ga),J.init(t,e)}),Ns=f("$ZodISOTime",(t,e)=>{e.pattern??(e.pattern=Ha(e)),J.init(t,e)}),Cs=f("$ZodISODuration",(t,e)=>{e.pattern??(e.pattern=ja),J.init(t,e)}),Rs=f("$ZodIPv4",(t,e)=>{e.pattern??(e.pattern=Va),J.init(t,e),t._zod.onattach.push(r=>{const n=r._zod.bag;n.format="ipv4"})}),Ps=f("$ZodIPv6",(t,e)=>{e.pattern??(e.pattern=Ba),J.init(t,e),t._zod.onattach.push(r=>{const n=r._zod.bag;n.format="ipv6"}),t._zod.check=r=>{try{new URL(`http://[${r.value}]`)}catch{r.issues.push({code:"invalid_format",format:"ipv6",input:r.value,inst:t,continue:!e.abort})}}}),Ms=f("$ZodCIDRv4",(t,e)=>{e.pattern??(e.pattern=Ja),J.init(t,e)}),js=f("$ZodCIDRv6",(t,e)=>{e.pattern??(e.pattern=Wa),J.init(t,e),t._zod.check=r=>{const[n,a]=r.value.split("/");try{if(!a)throw new Error;const s=Number(a);if(`${s}`!==a)throw new Error;if(s<0||s>128)throw new Error;new URL(`http://[${n}]`)}catch{r.issues.push({code:"invalid_format",format:"cidrv6",input:r.value,inst:t,continue:!e.abort})}}});function sn(t){if(t==="")return!0;if(t.length%4!==0)return!1;try{return atob(t),!0}catch{return!1}}const Ds=f("$ZodBase64",(t,e)=>{e.pattern??(e.pattern=qa),J.init(t,e),t._zod.onattach.push(r=>{r._zod.bag.contentEncoding="base64"}),t._zod.check=r=>{sn(r.value)||r.issues.push({code:"invalid_format",format:"base64",input:r.value,inst:t,continue:!e.abort})}});function Ls(t){if(!Qr.test(t))return!1;const e=t.replace(/[-_]/g,n=>n==="-"?"+":"/"),r=e.padEnd(Math.ceil(e.length/4)*4,"=");return sn(r)}const Us=f("$ZodBase64URL",(t,e)=>{e.pattern??(e.pattern=Qr),J.init(t,e),t._zod.onattach.push(r=>{r._zod.bag.contentEncoding="base64url"}),t._zod.check=r=>{Ls(r.value)||r.issues.push({code:"invalid_format",format:"base64url",input:r.value,inst:t,continue:!e.abort})}}),Fs=f("$ZodE164",(t,e)=>{e.pattern??(e.pattern=Ka),J.init(t,e)});function Vs(t,e=null){try{const r=t.split(".");if(r.length!==3)return!1;const[n]=r;if(!n)return!1;const a=JSON.parse(atob(n));return!("typ"in a&&(a==null?void 0:a.typ)!=="JWT"||!a.alg||e&&(!("alg"in a)||a.alg!==e))}catch{return!1}}const Bs=f("$ZodJWT",(t,e)=>{J.init(t,e),t._zod.check=r=>{Vs(r.value,e.alg)||r.issues.push({code:"invalid_format",format:"jwt",input:r.value,inst:t,continue:!e.abort})}}),on=f("$ZodNumber",(t,e)=>{F.init(t,e),t._zod.pattern=t._zod.bag.pattern??ts,t._zod.parse=(r,n)=>{if(e.coerce)try{r.value=Number(r.value)}catch{}const a=r.value;if(typeof a=="number"&&!Number.isNaN(a)&&Number.isFinite(a))return r;const s=typeof a=="number"?Number.isNaN(a)?"NaN":Number.isFinite(a)?void 0:"Infinity":void 0;return r.issues.push({expected:"number",code:"invalid_type",input:a,inst:t,...s?{received:s}:{}}),r}}),Js=f("$ZodNumber",(t,e)=>{os.init(t,e),on.init(t,e)}),Ws=f("$ZodBoolean",(t,e)=>{F.init(t,e),t._zod.pattern=rs,t._zod.parse=(r,n)=>{if(e.coerce)try{r.value=!!r.value}catch{}const a=r.value;return typeof a=="boolean"||r.issues.push({expected:"boolean",code:"invalid_type",input:a,inst:t}),r}}),qs=f("$ZodNull",(t,e)=>{F.init(t,e),t._zod.pattern=ns,t._zod.values=new Set([null]),t._zod.parse=(r,n)=>{const a=r.value;return a===null||r.issues.push({expected:"null",code:"invalid_type",input:a,inst:t}),r}}),Ys=f("$ZodUnknown",(t,e)=>{F.init(t,e),t._zod.parse=r=>r}),Ks=f("$ZodNever",(t,e)=>{F.init(t,e),t._zod.parse=(r,n)=>(r.issues.push({expected:"never",code:"invalid_type",input:r.value,inst:t}),r)});function Jt(t,e,r){t.issues.length&&e.issues.push(...ye(r,t.issues)),e.value[r]=t.value}const Gs=f("$ZodArray",(t,e)=>{F.init(t,e),t._zod.parse=(r,n)=>{const a=r.value;if(!Array.isArray(a))return r.issues.push({expected:"array",code:"invalid_type",input:a,inst:t}),r;r.value=Array(a.length);const s=[];for(let o=0;o<a.length;o++){const i=a[o],c=e.element._zod.run({value:i,issues:[]},n);c instanceof Promise?s.push(c.then(u=>Jt(u,r,o))):Jt(c,r,o)}return s.length?Promise.all(s).then(()=>r):r}});function qe(t,e,r){t.issues.length&&e.issues.push(...ye(r,t.issues)),e.value[r]=t.value}function Wt(t,e,r,n){t.issues.length?n[r]===void 0?r in n?e.value[r]=void 0:e.value[r]=t.value:e.issues.push(...ye(r,t.issues)):t.value===void 0?r in n&&(e.value[r]=void 0):e.value[r]=t.value}const Hs=f("$ZodObject",(t,e)=>{F.init(t,e);const r=Ot(()=>{const y=Object.keys(e.shape);for(const O of y)if(!(e.shape[O]instanceof F))throw new Error(`Invalid element at key "${O}": expected a Zod schema`);const x=ya(e.shape);return{shape:e.shape,keys:y,keySet:new Set(y),numKeys:y.length,optionalKeys:new Set(x)}});L(t._zod,"propValues",()=>{const y=e.shape,x={};for(const O in y){const w=y[O]._zod;if(w.values){x[O]??(x[O]=new Set);for(const Z of w.values)x[O].add(Z)}}return x});const n=y=>{const x=new ys(["shape","payload","ctx"]),O=r.value,w=h=>{const v=$e(h);return`shape[${v}]._zod.run({ value: input[${v}], issues: [] }, ctx)`};x.write("const input = payload.value;");const Z=Object.create(null);let g=0;for(const h of O.keys)Z[h]=`key_${g++}`;x.write("const newResult = {}");for(const h of O.keys)if(O.optionalKeys.has(h)){const v=Z[h];x.write(`const ${v} = ${w(h)};`);const p=$e(h);x.write(`
        if (${v}.issues.length) {
          if (input[${p}] === undefined) {
            if (${p} in input) {
              newResult[${p}] = undefined;
            }
          } else {
            payload.issues = payload.issues.concat(
              ${v}.issues.map((iss) => ({
                ...iss,
                path: iss.path ? [${p}, ...iss.path] : [${p}],
              }))
            );
          }
        } else if (${v}.value === undefined) {
          if (${p} in input) newResult[${p}] = undefined;
        } else {
          newResult[${p}] = ${v}.value;
        }
        `)}else{const v=Z[h];x.write(`const ${v} = ${w(h)};`),x.write(`
          if (${v}.issues.length) payload.issues = payload.issues.concat(${v}.issues.map(iss => ({
            ...iss,
            path: iss.path ? [${$e(h)}, ...iss.path] : [${$e(h)}]
          })));`),x.write(`newResult[${$e(h)}] = ${v}.value`)}x.write("payload.value = newResult;"),x.write("return payload;");const l=x.compile();return(h,v)=>l(y,h,v)};let a;const s=Ge,o=!Jr.jitless,c=o&&ga.value,u=e.catchall;let d;t._zod.parse=(y,x)=>{d??(d=r.value);const O=y.value;if(!s(O))return y.issues.push({expected:"object",code:"invalid_type",input:O,inst:t}),y;const w=[];if(o&&c&&(x==null?void 0:x.async)===!1&&x.jitless!==!0)a||(a=n(e.shape)),y=a(y,x);else{y.value={};const v=d.shape;for(const p of d.keys){const $=v[p],A=$._zod.run({value:O[p],issues:[]},x),z=$._zod.optin==="optional"&&$._zod.optout==="optional";A instanceof Promise?w.push(A.then(q=>z?Wt(q,y,p,O):qe(q,y,p))):z?Wt(A,y,p,O):qe(A,y,p)}}if(!u)return w.length?Promise.all(w).then(()=>y):y;const Z=[],g=d.keySet,l=u._zod,h=l.def.type;for(const v of Object.keys(O)){if(g.has(v))continue;if(h==="never"){Z.push(v);continue}const p=l.run({value:O[v],issues:[]},x);p instanceof Promise?w.push(p.then($=>qe($,y,v))):qe(p,y,v)}return Z.length&&y.issues.push({code:"unrecognized_keys",keys:Z,input:O,inst:t}),w.length?Promise.all(w).then(()=>y):y}});function qt(t,e,r,n){for(const a of t)if(a.issues.length===0)return e.value=a.value,e;return e.issues.push({code:"invalid_union",input:e.value,inst:r,errors:t.map(a=>a.issues.map(s=>he(s,n,fe())))}),e}const un=f("$ZodUnion",(t,e)=>{F.init(t,e),L(t._zod,"optin",()=>e.options.some(r=>r._zod.optin==="optional")?"optional":void 0),L(t._zod,"optout",()=>e.options.some(r=>r._zod.optout==="optional")?"optional":void 0),L(t._zod,"values",()=>{if(e.options.every(r=>r._zod.values))return new Set(e.options.flatMap(r=>Array.from(r._zod.values)))}),L(t._zod,"pattern",()=>{if(e.options.every(r=>r._zod.pattern)){const r=e.options.map(n=>n._zod.pattern);return new RegExp(`^(${r.map(n=>At(n.source)).join("|")})$`)}}),t._zod.parse=(r,n)=>{let a=!1;const s=[];for(const o of e.options){const i=o._zod.run({value:r.value,issues:[]},n);if(i instanceof Promise)s.push(i),a=!0;else{if(i.issues.length===0)return i;s.push(i)}}return a?Promise.all(s).then(o=>qt(o,r,t,n)):qt(s,r,t,n)}}),Xs=f("$ZodDiscriminatedUnion",(t,e)=>{un.init(t,e);const r=t._zod.parse;L(t._zod,"propValues",()=>{const a={};for(const s of e.options){const o=s._zod.propValues;if(!o||Object.keys(o).length===0)throw new Error(`Invalid discriminated union option at index "${e.options.indexOf(s)}"`);for(const[i,c]of Object.entries(o)){a[i]||(a[i]=new Set);for(const u of c)a[i].add(u)}}return a});const n=Ot(()=>{const a=e.options,s=new Map;for(const o of a){const i=o._zod.propValues[e.discriminator];if(!i||i.size===0)throw new Error(`Invalid discriminated union option at index "${e.options.indexOf(o)}"`);for(const c of i){if(s.has(c))throw new Error(`Duplicate discriminator value "${String(c)}"`);s.set(c,o)}}return s});t._zod.parse=(a,s)=>{const o=a.value;if(!Ge(o))return a.issues.push({code:"invalid_type",expected:"object",input:o,inst:t}),a;const i=n.value.get(o==null?void 0:o[e.discriminator]);return i?i._zod.run(a,s):e.unionFallback?r(a,s):(a.issues.push({code:"invalid_union",errors:[],note:"No matching discriminator",input:o,path:[e.discriminator],inst:t}),a)}}),Qs=f("$ZodIntersection",(t,e)=>{F.init(t,e),t._zod.parse=(r,n)=>{const a=r.value,s=e.left._zod.run({value:a,issues:[]},n),o=e.right._zod.run({value:a,issues:[]},n);return s instanceof Promise||o instanceof Promise?Promise.all([s,o]).then(([c,u])=>Yt(r,c,u)):Yt(r,s,o)}});function wt(t,e){if(t===e)return{valid:!0,data:t};if(t instanceof Date&&e instanceof Date&&+t==+e)return{valid:!0,data:t};if(He(t)&&He(e)){const r=Object.keys(e),n=Object.keys(t).filter(s=>r.indexOf(s)!==-1),a={...t,...e};for(const s of n){const o=wt(t[s],e[s]);if(!o.valid)return{valid:!1,mergeErrorPath:[s,...o.mergeErrorPath]};a[s]=o.data}return{valid:!0,data:a}}if(Array.isArray(t)&&Array.isArray(e)){if(t.length!==e.length)return{valid:!1,mergeErrorPath:[]};const r=[];for(let n=0;n<t.length;n++){const a=t[n],s=e[n],o=wt(a,s);if(!o.valid)return{valid:!1,mergeErrorPath:[n,...o.mergeErrorPath]};r.push(o.data)}return{valid:!0,data:r}}return{valid:!1,mergeErrorPath:[]}}function Yt(t,e,r){if(e.issues.length&&t.issues.push(...e.issues),r.issues.length&&t.issues.push(...r.issues),Ae(t))return t;const n=wt(e.value,r.value);if(!n.valid)throw new Error(`Unmergable intersection. Error path: ${JSON.stringify(n.mergeErrorPath)}`);return t.value=n.data,t}const ei=f("$ZodRecord",(t,e)=>{F.init(t,e),t._zod.parse=(r,n)=>{const a=r.value;if(!He(a))return r.issues.push({expected:"record",code:"invalid_type",input:a,inst:t}),r;const s=[];if(e.keyType._zod.values){const o=e.keyType._zod.values;r.value={};for(const c of o)if(typeof c=="string"||typeof c=="number"||typeof c=="symbol"){const u=e.valueType._zod.run({value:a[c],issues:[]},n);u instanceof Promise?s.push(u.then(d=>{d.issues.length&&r.issues.push(...ye(c,d.issues)),r.value[c]=d.value})):(u.issues.length&&r.issues.push(...ye(c,u.issues)),r.value[c]=u.value)}let i;for(const c in a)o.has(c)||(i=i??[],i.push(c));i&&i.length>0&&r.issues.push({code:"unrecognized_keys",input:a,inst:t,keys:i})}else{r.value={};for(const o of Reflect.ownKeys(a)){if(o==="__proto__")continue;const i=e.keyType._zod.run({value:o,issues:[]},n);if(i instanceof Promise)throw new Error("Async schemas not supported in object keys currently");if(i.issues.length){r.issues.push({origin:"record",code:"invalid_key",issues:i.issues.map(u=>he(u,n,fe())),input:o,path:[o],inst:t}),r.value[i.value]=i.value;continue}const c=e.valueType._zod.run({value:a[o],issues:[]},n);c instanceof Promise?s.push(c.then(u=>{u.issues.length&&r.issues.push(...ye(o,u.issues)),r.value[i.value]=u.value})):(c.issues.length&&r.issues.push(...ye(o,c.issues)),r.value[i.value]=c.value)}}return s.length?Promise.all(s).then(()=>r):r}}),ti=f("$ZodEnum",(t,e)=>{F.init(t,e);const r=Wr(e.entries);t._zod.values=new Set(r),t._zod.pattern=new RegExp(`^(${r.filter(n=>va.has(typeof n)).map(n=>typeof n=="string"?Ve(n):n.toString()).join("|")})$`),t._zod.parse=(n,a)=>{const s=n.value;return t._zod.values.has(s)||n.issues.push({code:"invalid_value",values:r,input:s,inst:t}),n}}),ri=f("$ZodLiteral",(t,e)=>{F.init(t,e),t._zod.values=new Set(e.values),t._zod.pattern=new RegExp(`^(${e.values.map(r=>typeof r=="string"?Ve(r):r?r.toString():String(r)).join("|")})$`),t._zod.parse=(r,n)=>{const a=r.value;return t._zod.values.has(a)||r.issues.push({code:"invalid_value",values:e.values,input:a,inst:t}),r}}),ni=f("$ZodTransform",(t,e)=>{F.init(t,e),t._zod.parse=(r,n)=>{const a=e.transform(r.value,r);if(n.async)return(a instanceof Promise?a:Promise.resolve(a)).then(o=>(r.value=o,r));if(a instanceof Promise)throw new Ce;return r.value=a,r}}),ai=f("$ZodOptional",(t,e)=>{F.init(t,e),t._zod.optin="optional",t._zod.optout="optional",L(t._zod,"values",()=>e.innerType._zod.values?new Set([...e.innerType._zod.values,void 0]):void 0),L(t._zod,"pattern",()=>{const r=e.innerType._zod.pattern;return r?new RegExp(`^(${At(r.source)})?$`):void 0}),t._zod.parse=(r,n)=>e.innerType._zod.optin==="optional"?e.innerType._zod.run(r,n):r.value===void 0?r:e.innerType._zod.run(r,n)}),si=f("$ZodNullable",(t,e)=>{F.init(t,e),L(t._zod,"optin",()=>e.innerType._zod.optin),L(t._zod,"optout",()=>e.innerType._zod.optout),L(t._zod,"pattern",()=>{const r=e.innerType._zod.pattern;return r?new RegExp(`^(${At(r.source)}|null)$`):void 0}),L(t._zod,"values",()=>e.innerType._zod.values?new Set([...e.innerType._zod.values,null]):void 0),t._zod.parse=(r,n)=>r.value===null?r:e.innerType._zod.run(r,n)}),ii=f("$ZodDefault",(t,e)=>{F.init(t,e),t._zod.optin="optional",L(t._zod,"values",()=>e.innerType._zod.values),t._zod.parse=(r,n)=>{if(r.value===void 0)return r.value=e.defaultValue,r;const a=e.innerType._zod.run(r,n);return a instanceof Promise?a.then(s=>Kt(s,e)):Kt(a,e)}});function Kt(t,e){return t.value===void 0&&(t.value=e.defaultValue),t}const oi=f("$ZodPrefault",(t,e)=>{F.init(t,e),t._zod.optin="optional",L(t._zod,"values",()=>e.innerType._zod.values),t._zod.parse=(r,n)=>(r.value===void 0&&(r.value=e.defaultValue),e.innerType._zod.run(r,n))}),ui=f("$ZodNonOptional",(t,e)=>{F.init(t,e),L(t._zod,"values",()=>{const r=e.innerType._zod.values;return r?new Set([...r].filter(n=>n!==void 0)):void 0}),t._zod.parse=(r,n)=>{const a=e.innerType._zod.run(r,n);return a instanceof Promise?a.then(s=>Gt(s,t)):Gt(a,t)}});function Gt(t,e){return!t.issues.length&&t.value===void 0&&t.issues.push({code:"invalid_type",expected:"nonoptional",input:t.value,inst:e}),t}const ci=f("$ZodCatch",(t,e)=>{F.init(t,e),t._zod.optin="optional",L(t._zod,"optout",()=>e.innerType._zod.optout),L(t._zod,"values",()=>e.innerType._zod.values),t._zod.parse=(r,n)=>{const a=e.innerType._zod.run(r,n);return a instanceof Promise?a.then(s=>(r.value=s.value,s.issues.length&&(r.value=e.catchValue({...r,error:{issues:s.issues.map(o=>he(o,n,fe()))},input:r.value}),r.issues=[]),r)):(r.value=a.value,a.issues.length&&(r.value=e.catchValue({...r,error:{issues:a.issues.map(s=>he(s,n,fe()))},input:r.value}),r.issues=[]),r)}}),li=f("$ZodPipe",(t,e)=>{F.init(t,e),L(t._zod,"values",()=>e.in._zod.values),L(t._zod,"optin",()=>e.in._zod.optin),L(t._zod,"optout",()=>e.out._zod.optout),t._zod.parse=(r,n)=>{const a=e.in._zod.run(r,n);return a instanceof Promise?a.then(s=>Ht(s,e,n)):Ht(a,e,n)}});function Ht(t,e,r){return Ae(t)?t:e.out._zod.run({value:t.value,issues:t.issues},r)}const di=f("$ZodReadonly",(t,e)=>{F.init(t,e),L(t._zod,"propValues",()=>e.innerType._zod.propValues),L(t._zod,"values",()=>e.innerType._zod.values),L(t._zod,"optin",()=>e.innerType._zod.optin),L(t._zod,"optout",()=>e.innerType._zod.optout),t._zod.parse=(r,n)=>{const a=e.innerType._zod.run(r,n);return a instanceof Promise?a.then(Xt):Xt(a)}});function Xt(t){return t.value=Object.freeze(t.value),t}const pi=f("$ZodLazy",(t,e)=>{F.init(t,e),L(t._zod,"innerType",()=>e.getter()),L(t._zod,"pattern",()=>t._zod.innerType._zod.pattern),L(t._zod,"propValues",()=>t._zod.innerType._zod.propValues),L(t._zod,"optin",()=>t._zod.innerType._zod.optin),L(t._zod,"optout",()=>t._zod.innerType._zod.optout),t._zod.parse=(r,n)=>t._zod.innerType._zod.run(r,n)}),fi=f("$ZodCustom",(t,e)=>{re.init(t,e),F.init(t,e),t._zod.parse=(r,n)=>r,t._zod.check=r=>{const n=r.value,a=e.fn(n);if(a instanceof Promise)return a.then(s=>Qt(s,r,n,t));Qt(a,r,n,t)}});function Qt(t,e,r,n){if(!t){const a={code:"custom",input:r,inst:n,path:[...n._zod.def.path??[]],continue:!n._zod.def.abort};n._zod.def.params&&(a.params=n._zod.def.params),e.issues.push(Re(a))}}class cn{constructor(){this._map=new Map,this._idmap=new Map}add(e,...r){const n=r[0];if(this._map.set(e,n),n&&typeof n=="object"&&"id"in n){if(this._idmap.has(n.id))throw new Error(`ID ${n.id} already exists in the registry`);this._idmap.set(n.id,e)}return this}clear(){return this._map=new Map,this._idmap=new Map,this}remove(e){const r=this._map.get(e);return r&&typeof r=="object"&&"id"in r&&this._idmap.delete(r.id),this._map.delete(e),this}get(e){const r=e._zod.parent;if(r){const n={...this.get(r)??{}};return delete n.id,{...n,...this._map.get(e)}}return this._map.get(e)}has(e){return this._map.has(e)}}function hi(){return new cn}const Oe=hi();function mi(t,e){return new t({type:"string",...T(e)})}function gi(t,e){return new t({type:"string",format:"email",check:"string_format",abort:!1,...T(e)})}function er(t,e){return new t({type:"string",format:"guid",check:"string_format",abort:!1,...T(e)})}function vi(t,e){return new t({type:"string",format:"uuid",check:"string_format",abort:!1,...T(e)})}function yi(t,e){return new t({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v4",...T(e)})}function _i(t,e){return new t({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v6",...T(e)})}function bi(t,e){return new t({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v7",...T(e)})}function wi(t,e){return new t({type:"string",format:"url",check:"string_format",abort:!1,...T(e)})}function ki(t,e){return new t({type:"string",format:"emoji",check:"string_format",abort:!1,...T(e)})}function xi(t,e){return new t({type:"string",format:"nanoid",check:"string_format",abort:!1,...T(e)})}function Ii(t,e){return new t({type:"string",format:"cuid",check:"string_format",abort:!1,...T(e)})}function Si(t,e){return new t({type:"string",format:"cuid2",check:"string_format",abort:!1,...T(e)})}function Ti(t,e){return new t({type:"string",format:"ulid",check:"string_format",abort:!1,...T(e)})}function Ei(t,e){return new t({type:"string",format:"xid",check:"string_format",abort:!1,...T(e)})}function Zi(t,e){return new t({type:"string",format:"ksuid",check:"string_format",abort:!1,...T(e)})}function $i(t,e){return new t({type:"string",format:"ipv4",check:"string_format",abort:!1,...T(e)})}function Oi(t,e){return new t({type:"string",format:"ipv6",check:"string_format",abort:!1,...T(e)})}function zi(t,e){return new t({type:"string",format:"cidrv4",check:"string_format",abort:!1,...T(e)})}function Ai(t,e){return new t({type:"string",format:"cidrv6",check:"string_format",abort:!1,...T(e)})}function ln(t,e){return new t({type:"string",format:"base64",check:"string_format",abort:!1,...T(e)})}function Ni(t,e){return new t({type:"string",format:"base64url",check:"string_format",abort:!1,...T(e)})}function Ci(t,e){return new t({type:"string",format:"e164",check:"string_format",abort:!1,...T(e)})}function Ri(t,e){return new t({type:"string",format:"jwt",check:"string_format",abort:!1,...T(e)})}function Pi(t,e){return new t({type:"string",format:"datetime",check:"string_format",offset:!1,local:!1,precision:null,...T(e)})}function Mi(t,e){return new t({type:"string",format:"date",check:"string_format",...T(e)})}function ji(t,e){return new t({type:"string",format:"time",check:"string_format",precision:null,...T(e)})}function Di(t,e){return new t({type:"string",format:"duration",check:"string_format",...T(e)})}function Li(t,e){return new t({type:"number",checks:[],...T(e)})}function Ui(t,e){return new t({type:"number",check:"number_format",abort:!1,format:"safeint",...T(e)})}function Fi(t,e){return new t({type:"boolean",...T(e)})}function Vi(t,e){return new t({type:"null",...T(e)})}function Bi(t){return new t({type:"unknown"})}function Ji(t,e){return new t({type:"never",...T(e)})}function tr(t,e){return new nn({check:"less_than",...T(e),value:t,inclusive:!1})}function mt(t,e){return new nn({check:"less_than",...T(e),value:t,inclusive:!0})}function rr(t,e){return new an({check:"greater_than",...T(e),value:t,inclusive:!1})}function gt(t,e){return new an({check:"greater_than",...T(e),value:t,inclusive:!0})}function nr(t,e){return new is({check:"multiple_of",...T(e),value:t})}function dn(t,e){return new us({check:"max_length",...T(e),maximum:t})}function Xe(t,e){return new cs({check:"min_length",...T(e),minimum:t})}function pn(t,e){return new ls({check:"length_equals",...T(e),length:t})}function Wi(t,e){return new ds({check:"string_format",format:"regex",...T(e),pattern:t})}function qi(t){return new ps({check:"string_format",format:"lowercase",...T(t)})}function Yi(t){return new fs({check:"string_format",format:"uppercase",...T(t)})}function Ki(t,e){return new hs({check:"string_format",format:"includes",...T(e),includes:t})}function Gi(t,e){return new ms({check:"string_format",format:"starts_with",...T(e),prefix:t})}function Hi(t,e){return new gs({check:"string_format",format:"ends_with",...T(e),suffix:t})}function Be(t){return new vs({check:"overwrite",tx:t})}function Xi(t){return Be(e=>e.normalize(t))}function Qi(){return Be(t=>t.trim())}function eo(){return Be(t=>t.toLowerCase())}function to(){return Be(t=>t.toUpperCase())}function ro(t,e,r){return new t({type:"array",element:e,...T(r)})}function no(t,e,r){const n=T(r);return n.abort??(n.abort=!0),new t({type:"custom",check:"custom",fn:e,...n})}function ao(t,e,r){return new t({type:"custom",check:"custom",fn:e,...T(r)})}class ar{constructor(e){this.counter=0,this.metadataRegistry=(e==null?void 0:e.metadata)??Oe,this.target=(e==null?void 0:e.target)??"draft-2020-12",this.unrepresentable=(e==null?void 0:e.unrepresentable)??"throw",this.override=(e==null?void 0:e.override)??(()=>{}),this.io=(e==null?void 0:e.io)??"output",this.seen=new Map}process(e,r={path:[],schemaPath:[]}){var y,x,O;var n;const a=e._zod.def,s={guid:"uuid",url:"uri",datetime:"date-time",json_string:"json-string",regex:""},o=this.seen.get(e);if(o)return o.count++,r.schemaPath.includes(e)&&(o.cycle=r.path),o.schema;const i={schema:{},count:1,cycle:void 0,path:r.path};this.seen.set(e,i);const c=(x=(y=e._zod).toJSONSchema)==null?void 0:x.call(y);if(c)i.schema=c;else{const w={...r,schemaPath:[...r.schemaPath,e],path:r.path},Z=e._zod.parent;if(Z)i.ref=Z,this.process(Z,w),this.seen.get(Z).isParent=!0;else{const g=i.schema;switch(a.type){case"string":{const l=g;l.type="string";const{minimum:h,maximum:v,format:p,patterns:$,contentEncoding:A}=e._zod.bag;if(typeof h=="number"&&(l.minLength=h),typeof v=="number"&&(l.maxLength=v),p&&(l.format=s[p]??p,l.format===""&&delete l.format),A&&(l.contentEncoding=A),$&&$.size>0){const z=[...$];z.length===1?l.pattern=z[0].source:z.length>1&&(i.schema.allOf=[...z.map(q=>({...this.target==="draft-7"?{type:"string"}:{},pattern:q.source}))])}break}case"number":{const l=g,{minimum:h,maximum:v,format:p,multipleOf:$,exclusiveMaximum:A,exclusiveMinimum:z}=e._zod.bag;typeof p=="string"&&p.includes("int")?l.type="integer":l.type="number",typeof z=="number"&&(l.exclusiveMinimum=z),typeof h=="number"&&(l.minimum=h,typeof z=="number"&&(z>=h?delete l.minimum:delete l.exclusiveMinimum)),typeof A=="number"&&(l.exclusiveMaximum=A),typeof v=="number"&&(l.maximum=v,typeof A=="number"&&(A<=v?delete l.maximum:delete l.exclusiveMaximum)),typeof $=="number"&&(l.multipleOf=$);break}case"boolean":{const l=g;l.type="boolean";break}case"bigint":{if(this.unrepresentable==="throw")throw new Error("BigInt cannot be represented in JSON Schema");break}case"symbol":{if(this.unrepresentable==="throw")throw new Error("Symbols cannot be represented in JSON Schema");break}case"null":{g.type="null";break}case"any":break;case"unknown":break;case"undefined":{if(this.unrepresentable==="throw")throw new Error("Undefined cannot be represented in JSON Schema");break}case"void":{if(this.unrepresentable==="throw")throw new Error("Void cannot be represented in JSON Schema");break}case"never":{g.not={};break}case"date":{if(this.unrepresentable==="throw")throw new Error("Date cannot be represented in JSON Schema");break}case"array":{const l=g,{minimum:h,maximum:v}=e._zod.bag;typeof h=="number"&&(l.minItems=h),typeof v=="number"&&(l.maxItems=v),l.type="array",l.items=this.process(a.element,{...w,path:[...w.path,"items"]});break}case"object":{const l=g;l.type="object",l.properties={};const h=a.shape;for(const $ in h)l.properties[$]=this.process(h[$],{...w,path:[...w.path,"properties",$]});const v=new Set(Object.keys(h)),p=new Set([...v].filter($=>{const A=a.shape[$]._zod;return this.io==="input"?A.optin===void 0:A.optout===void 0}));p.size>0&&(l.required=Array.from(p)),((O=a.catchall)==null?void 0:O._zod.def.type)==="never"?l.additionalProperties=!1:a.catchall?a.catchall&&(l.additionalProperties=this.process(a.catchall,{...w,path:[...w.path,"additionalProperties"]})):this.io==="output"&&(l.additionalProperties=!1);break}case"union":{const l=g;l.anyOf=a.options.map((h,v)=>this.process(h,{...w,path:[...w.path,"anyOf",v]}));break}case"intersection":{const l=g,h=this.process(a.left,{...w,path:[...w.path,"allOf",0]}),v=this.process(a.right,{...w,path:[...w.path,"allOf",1]}),p=A=>"allOf"in A&&Object.keys(A).length===1,$=[...p(h)?h.allOf:[h],...p(v)?v.allOf:[v]];l.allOf=$;break}case"tuple":{const l=g;l.type="array";const h=a.items.map(($,A)=>this.process($,{...w,path:[...w.path,"prefixItems",A]}));if(this.target==="draft-2020-12"?l.prefixItems=h:l.items=h,a.rest){const $=this.process(a.rest,{...w,path:[...w.path,"items"]});this.target==="draft-2020-12"?l.items=$:l.additionalItems=$}a.rest&&(l.items=this.process(a.rest,{...w,path:[...w.path,"items"]}));const{minimum:v,maximum:p}=e._zod.bag;typeof v=="number"&&(l.minItems=v),typeof p=="number"&&(l.maxItems=p);break}case"record":{const l=g;l.type="object",l.propertyNames=this.process(a.keyType,{...w,path:[...w.path,"propertyNames"]}),l.additionalProperties=this.process(a.valueType,{...w,path:[...w.path,"additionalProperties"]});break}case"map":{if(this.unrepresentable==="throw")throw new Error("Map cannot be represented in JSON Schema");break}case"set":{if(this.unrepresentable==="throw")throw new Error("Set cannot be represented in JSON Schema");break}case"enum":{const l=g,h=Wr(a.entries);h.every(v=>typeof v=="number")&&(l.type="number"),h.every(v=>typeof v=="string")&&(l.type="string"),l.enum=h;break}case"literal":{const l=g,h=[];for(const v of a.values)if(v===void 0){if(this.unrepresentable==="throw")throw new Error("Literal `undefined` cannot be represented in JSON Schema")}else if(typeof v=="bigint"){if(this.unrepresentable==="throw")throw new Error("BigInt literals cannot be represented in JSON Schema");h.push(Number(v))}else h.push(v);if(h.length!==0)if(h.length===1){const v=h[0];l.type=v===null?"null":typeof v,l.const=v}else h.every(v=>typeof v=="number")&&(l.type="number"),h.every(v=>typeof v=="string")&&(l.type="string"),h.every(v=>typeof v=="boolean")&&(l.type="string"),h.every(v=>v===null)&&(l.type="null"),l.enum=h;break}case"file":{const l=g,h={type:"string",format:"binary",contentEncoding:"binary"},{minimum:v,maximum:p,mime:$}=e._zod.bag;v!==void 0&&(h.minLength=v),p!==void 0&&(h.maxLength=p),$?$.length===1?(h.contentMediaType=$[0],Object.assign(l,h)):l.anyOf=$.map(A=>({...h,contentMediaType:A})):Object.assign(l,h);break}case"transform":{if(this.unrepresentable==="throw")throw new Error("Transforms cannot be represented in JSON Schema");break}case"nullable":{const l=this.process(a.innerType,w);g.anyOf=[l,{type:"null"}];break}case"nonoptional":{this.process(a.innerType,w),i.ref=a.innerType;break}case"success":{const l=g;l.type="boolean";break}case"default":{this.process(a.innerType,w),i.ref=a.innerType,g.default=JSON.parse(JSON.stringify(a.defaultValue));break}case"prefault":{this.process(a.innerType,w),i.ref=a.innerType,this.io==="input"&&(g._prefault=JSON.parse(JSON.stringify(a.defaultValue)));break}case"catch":{this.process(a.innerType,w),i.ref=a.innerType;let l;try{l=a.catchValue(void 0)}catch{throw new Error("Dynamic catch values are not supported in JSON Schema")}g.default=l;break}case"nan":{if(this.unrepresentable==="throw")throw new Error("NaN cannot be represented in JSON Schema");break}case"template_literal":{const l=g,h=e._zod.pattern;if(!h)throw new Error("Pattern not found in template literal");l.type="string",l.pattern=h.source;break}case"pipe":{const l=this.io==="input"?a.in._zod.def.type==="transform"?a.out:a.in:a.out;this.process(l,w),i.ref=l;break}case"readonly":{this.process(a.innerType,w),i.ref=a.innerType,g.readOnly=!0;break}case"promise":{this.process(a.innerType,w),i.ref=a.innerType;break}case"optional":{this.process(a.innerType,w),i.ref=a.innerType;break}case"lazy":{const l=e._zod.innerType;this.process(l,w),i.ref=l;break}case"custom":{if(this.unrepresentable==="throw")throw new Error("Custom types cannot be represented in JSON Schema");break}}}}const u=this.metadataRegistry.get(e);return u&&Object.assign(i.schema,u),this.io==="input"&&H(e)&&(delete i.schema.examples,delete i.schema.default),this.io==="input"&&i.schema._prefault&&((n=i.schema).default??(n.default=i.schema._prefault)),delete i.schema._prefault,this.seen.get(e).schema}emit(e,r){var d,y,x,O,w,Z;const n={cycles:(r==null?void 0:r.cycles)??"ref",reused:(r==null?void 0:r.reused)??"inline",external:(r==null?void 0:r.external)??void 0},a=this.seen.get(e);if(!a)throw new Error("Unprocessed schema. This is a bug in Zod.");const s=g=>{var $;const l=this.target==="draft-2020-12"?"$defs":"definitions";if(n.external){const A=($=n.external.registry.get(g[0]))==null?void 0:$.id,z=n.external.uri??(ve=>ve);if(A)return{ref:z(A)};const q=g[1].defId??g[1].schema.id??`schema${this.counter++}`;return g[1].defId=q,{defId:q,ref:`${z("__shared")}#/${l}/${q}`}}if(g[1]===a)return{ref:"#"};const v=`#/${l}/`,p=g[1].schema.id??`__schema${this.counter++}`;return{defId:p,ref:v+p}},o=g=>{if(g[1].schema.$ref)return;const l=g[1],{ref:h,defId:v}=s(g);l.def={...l.schema},v&&(l.defId=v);const p=l.schema;for(const $ in p)delete p[$];p.$ref=h};if(n.cycles==="throw")for(const g of this.seen.entries()){const l=g[1];if(l.cycle)throw new Error(`Cycle detected: #/${(d=l.cycle)==null?void 0:d.join("/")}/<root>

Set the \`cycles\` parameter to \`"ref"\` to resolve cyclical schemas with defs.`)}for(const g of this.seen.entries()){const l=g[1];if(e===g[0]){o(g);continue}if(n.external){const v=(y=n.external.registry.get(g[0]))==null?void 0:y.id;if(e!==g[0]&&v){o(g);continue}}if((x=this.metadataRegistry.get(g[0]))==null?void 0:x.id){o(g);continue}if(l.cycle){o(g);continue}if(l.count>1&&n.reused==="ref"){o(g);continue}}const i=(g,l)=>{const h=this.seen.get(g),v=h.def??h.schema,p={...v};if(h.ref===null)return;const $=h.ref;if(h.ref=null,$){i($,l);const A=this.seen.get($).schema;A.$ref&&l.target==="draft-7"?(v.allOf=v.allOf??[],v.allOf.push(A)):(Object.assign(v,A),Object.assign(v,p))}h.isParent||this.override({zodSchema:g,jsonSchema:v,path:h.path??[]})};for(const g of[...this.seen.entries()].reverse())i(g[0],{target:this.target});const c={};if(this.target==="draft-2020-12"?c.$schema="https://json-schema.org/draft/2020-12/schema":this.target==="draft-7"?c.$schema="http://json-schema.org/draft-07/schema#":console.warn(`Invalid target: ${this.target}`),(O=n.external)!=null&&O.uri){const g=(w=n.external.registry.get(e))==null?void 0:w.id;if(!g)throw new Error("Schema is missing an `id` property");c.$id=n.external.uri(g)}Object.assign(c,a.def);const u=((Z=n.external)==null?void 0:Z.defs)??{};for(const g of this.seen.entries()){const l=g[1];l.def&&l.defId&&(u[l.defId]=l.def)}n.external||Object.keys(u).length>0&&(this.target==="draft-2020-12"?c.$defs=u:c.definitions=u);try{return JSON.parse(JSON.stringify(c))}catch{throw new Error("Error converting schema to JSON.")}}}function so(t,e){if(t instanceof cn){const n=new ar(e),a={};for(const i of t._idmap.entries()){const[c,u]=i;n.process(u)}const s={},o={registry:t,uri:e==null?void 0:e.uri,defs:a};for(const i of t._idmap.entries()){const[c,u]=i;s[c]=n.emit(u,{...e,external:o})}if(Object.keys(a).length>0){const i=n.target==="draft-2020-12"?"$defs":"definitions";s.__shared={[i]:a}}return{schemas:s}}const r=new ar(e);return r.process(t),r.emit(t,e)}function H(t,e){const r=e??{seen:new Set};if(r.seen.has(t))return!1;r.seen.add(t);const a=t._zod.def;switch(a.type){case"string":case"number":case"bigint":case"boolean":case"date":case"symbol":case"undefined":case"null":case"any":case"unknown":case"never":case"void":case"literal":case"enum":case"nan":case"file":case"template_literal":return!1;case"array":return H(a.element,r);case"object":{for(const s in a.shape)if(H(a.shape[s],r))return!0;return!1}case"union":{for(const s of a.options)if(H(s,r))return!0;return!1}case"intersection":return H(a.left,r)||H(a.right,r);case"tuple":{for(const s of a.items)if(H(s,r))return!0;return!!(a.rest&&H(a.rest,r))}case"record":return H(a.keyType,r)||H(a.valueType,r);case"map":return H(a.keyType,r)||H(a.valueType,r);case"set":return H(a.valueType,r);case"promise":case"optional":case"nonoptional":case"nullable":case"readonly":return H(a.innerType,r);case"lazy":return H(a.getter(),r);case"default":return H(a.innerType,r);case"prefault":return H(a.innerType,r);case"custom":return!1;case"transform":return!0;case"pipe":return H(a.in,r)||H(a.out,r);case"success":return!1;case"catch":return!1}throw new Error(`Unknown schema type: ${a.type}`)}const io=f("ZodISODateTime",(t,e)=>{zs.init(t,e),Y.init(t,e)});function oo(t){return Pi(io,t)}const uo=f("ZodISODate",(t,e)=>{As.init(t,e),Y.init(t,e)});function co(t){return Mi(uo,t)}const lo=f("ZodISOTime",(t,e)=>{Ns.init(t,e),Y.init(t,e)});function po(t){return ji(lo,t)}const fo=f("ZodISODuration",(t,e)=>{Cs.init(t,e),Y.init(t,e)});function ho(t){return Di(fo,t)}const mo=(t,e)=>{Kr.init(t,e),t.name="ZodError",Object.defineProperties(t,{format:{value:r=>Ea(t,r)},flatten:{value:r=>Ta(t,r)},addIssue:{value:r=>t.issues.push(r)},addIssues:{value:r=>t.issues.push(...r)},isEmpty:{get(){return t.issues.length===0}}})},ct=f("ZodError",mo,{Parent:Error}),go=Za(ct),vo=$a(ct),yo=Hr(ct),fn=Xr(ct),W=f("ZodType",(t,e)=>(F.init(t,e),t.def=e,Object.defineProperty(t,"_def",{value:e}),t.check=(...r)=>t.clone({...e,checks:[...e.checks??[],...r.map(n=>typeof n=="function"?{_zod:{check:n,def:{check:"custom"},onattach:[]}}:n)]}),t.clone=(r,n)=>we(t,r,n),t.brand=()=>t,t.register=(r,n)=>(r.add(t,n),t),t.parse=(r,n)=>go(t,r,n,{callee:t.parse}),t.safeParse=(r,n)=>yo(t,r,n),t.parseAsync=async(r,n)=>vo(t,r,n,{callee:t.parseAsync}),t.safeParseAsync=async(r,n)=>fn(t,r,n),t.spa=t.safeParseAsync,t.refine=(r,n)=>t.check(pu(r,n)),t.superRefine=r=>t.check(fu(r)),t.overwrite=r=>t.check(Be(r)),t.optional=()=>G(t),t.nullable=()=>or(t),t.nullish=()=>G(or(t)),t.nonoptional=r=>ru(t,r),t.array=()=>le(t),t.or=r=>Q([t,r]),t.and=r=>Wo(t,r),t.transform=r=>ur(t,Go(r)),t.default=r=>Qo(t,r),t.prefault=r=>tu(t,r),t.catch=r=>au(t,r),t.pipe=r=>ur(t,r),t.readonly=()=>ou(t),t.describe=r=>{const n=t.clone();return Oe.add(n,{description:r}),n},Object.defineProperty(t,"description",{get(){var r;return(r=Oe.get(t))==null?void 0:r.description},configurable:!0}),t.meta=(...r)=>{if(r.length===0)return Oe.get(t);const n=t.clone();return Oe.add(n,r[0]),n},t.isOptional=()=>t.safeParse(void 0).success,t.isNullable=()=>t.safeParse(null).success,t)),hn=f("_ZodString",(t,e)=>{Ct.init(t,e),W.init(t,e);const r=t._zod.bag;t.format=r.format??null,t.minLength=r.minimum??null,t.maxLength=r.maximum??null,t.regex=(...n)=>t.check(Wi(...n)),t.includes=(...n)=>t.check(Ki(...n)),t.startsWith=(...n)=>t.check(Gi(...n)),t.endsWith=(...n)=>t.check(Hi(...n)),t.min=(...n)=>t.check(Xe(...n)),t.max=(...n)=>t.check(dn(...n)),t.length=(...n)=>t.check(pn(...n)),t.nonempty=(...n)=>t.check(Xe(1,...n)),t.lowercase=n=>t.check(qi(n)),t.uppercase=n=>t.check(Yi(n)),t.trim=()=>t.check(Qi()),t.normalize=(...n)=>t.check(Xi(...n)),t.toLowerCase=()=>t.check(eo()),t.toUpperCase=()=>t.check(to())}),_o=f("ZodString",(t,e)=>{Ct.init(t,e),hn.init(t,e),t.email=r=>t.check(gi(bo,r)),t.url=r=>t.check(wi(wo,r)),t.jwt=r=>t.check(Ri(Ro,r)),t.emoji=r=>t.check(ki(ko,r)),t.guid=r=>t.check(er(sr,r)),t.uuid=r=>t.check(vi(Ye,r)),t.uuidv4=r=>t.check(yi(Ye,r)),t.uuidv6=r=>t.check(_i(Ye,r)),t.uuidv7=r=>t.check(bi(Ye,r)),t.nanoid=r=>t.check(xi(xo,r)),t.guid=r=>t.check(er(sr,r)),t.cuid=r=>t.check(Ii(Io,r)),t.cuid2=r=>t.check(Si(So,r)),t.ulid=r=>t.check(Ti(To,r)),t.base64=r=>t.check(ln(mn,r)),t.base64url=r=>t.check(Ni(No,r)),t.xid=r=>t.check(Ei(Eo,r)),t.ksuid=r=>t.check(Zi(Zo,r)),t.ipv4=r=>t.check($i($o,r)),t.ipv6=r=>t.check(Oi(Oo,r)),t.cidrv4=r=>t.check(zi(zo,r)),t.cidrv6=r=>t.check(Ai(Ao,r)),t.e164=r=>t.check(Ci(Co,r)),t.datetime=r=>t.check(oo(r)),t.date=r=>t.check(co(r)),t.time=r=>t.check(po(r)),t.duration=r=>t.check(ho(r))});function m(t){return mi(_o,t)}const Y=f("ZodStringFormat",(t,e)=>{J.init(t,e),hn.init(t,e)}),bo=f("ZodEmail",(t,e)=>{ks.init(t,e),Y.init(t,e)}),sr=f("ZodGUID",(t,e)=>{bs.init(t,e),Y.init(t,e)}),Ye=f("ZodUUID",(t,e)=>{ws.init(t,e),Y.init(t,e)}),wo=f("ZodURL",(t,e)=>{xs.init(t,e),Y.init(t,e)}),ko=f("ZodEmoji",(t,e)=>{Is.init(t,e),Y.init(t,e)}),xo=f("ZodNanoID",(t,e)=>{Ss.init(t,e),Y.init(t,e)}),Io=f("ZodCUID",(t,e)=>{Ts.init(t,e),Y.init(t,e)}),So=f("ZodCUID2",(t,e)=>{Es.init(t,e),Y.init(t,e)}),To=f("ZodULID",(t,e)=>{Zs.init(t,e),Y.init(t,e)}),Eo=f("ZodXID",(t,e)=>{$s.init(t,e),Y.init(t,e)}),Zo=f("ZodKSUID",(t,e)=>{Os.init(t,e),Y.init(t,e)}),$o=f("ZodIPv4",(t,e)=>{Rs.init(t,e),Y.init(t,e)}),Oo=f("ZodIPv6",(t,e)=>{Ps.init(t,e),Y.init(t,e)}),zo=f("ZodCIDRv4",(t,e)=>{Ms.init(t,e),Y.init(t,e)}),Ao=f("ZodCIDRv6",(t,e)=>{js.init(t,e),Y.init(t,e)}),mn=f("ZodBase64",(t,e)=>{Ds.init(t,e),Y.init(t,e)});function gn(t){return ln(mn,t)}const No=f("ZodBase64URL",(t,e)=>{Us.init(t,e),Y.init(t,e)}),Co=f("ZodE164",(t,e)=>{Fs.init(t,e),Y.init(t,e)}),Ro=f("ZodJWT",(t,e)=>{Bs.init(t,e),Y.init(t,e)}),vn=f("ZodNumber",(t,e)=>{on.init(t,e),W.init(t,e),t.gt=(n,a)=>t.check(rr(n,a)),t.gte=(n,a)=>t.check(gt(n,a)),t.min=(n,a)=>t.check(gt(n,a)),t.lt=(n,a)=>t.check(tr(n,a)),t.lte=(n,a)=>t.check(mt(n,a)),t.max=(n,a)=>t.check(mt(n,a)),t.int=n=>t.check(ir(n)),t.safe=n=>t.check(ir(n)),t.positive=n=>t.check(rr(0,n)),t.nonnegative=n=>t.check(gt(0,n)),t.negative=n=>t.check(tr(0,n)),t.nonpositive=n=>t.check(mt(0,n)),t.multipleOf=(n,a)=>t.check(nr(n,a)),t.step=(n,a)=>t.check(nr(n,a)),t.finite=()=>t;const r=t._zod.bag;t.minValue=Math.max(r.minimum??Number.NEGATIVE_INFINITY,r.exclusiveMinimum??Number.NEGATIVE_INFINITY)??null,t.maxValue=Math.min(r.maximum??Number.POSITIVE_INFINITY,r.exclusiveMaximum??Number.POSITIVE_INFINITY)??null,t.isInt=(r.format??"").includes("int")||Number.isSafeInteger(r.multipleOf??.5),t.isFinite=!0,t.format=r.format??null});function Pe(t){return Li(vn,t)}const Po=f("ZodNumberFormat",(t,e)=>{Js.init(t,e),vn.init(t,e)});function ir(t){return Ui(Po,t)}const Mo=f("ZodBoolean",(t,e)=>{Ws.init(t,e),W.init(t,e)});function K(t){return Fi(Mo,t)}const jo=f("ZodNull",(t,e)=>{qs.init(t,e),W.init(t,e)});function Do(t){return Vi(jo,t)}const Lo=f("ZodUnknown",(t,e)=>{Ys.init(t,e),W.init(t,e)});function B(){return Bi(Lo)}const Uo=f("ZodNever",(t,e)=>{Ks.init(t,e),W.init(t,e)});function te(t){return Ji(Uo,t)}const Fo=f("ZodArray",(t,e)=>{Gs.init(t,e),W.init(t,e),t.element=e.element,t.min=(r,n)=>t.check(Xe(r,n)),t.nonempty=r=>t.check(Xe(1,r)),t.max=(r,n)=>t.check(dn(r,n)),t.length=(r,n)=>t.check(pn(r,n)),t.unwrap=()=>t.element});function le(t,e){return ro(Fo,t,e)}const Rt=f("ZodObject",(t,e)=>{Hs.init(t,e),W.init(t,e),L(t,"shape",()=>e.shape),t.keyof=()=>lt(Object.keys(t._zod.def.shape)),t.catchall=r=>t.clone({...t._zod.def,catchall:r}),t.passthrough=()=>t.clone({...t._zod.def,catchall:B()}),t.loose=()=>t.clone({...t._zod.def,catchall:B()}),t.strict=()=>t.clone({...t._zod.def,catchall:te()}),t.strip=()=>t.clone({...t._zod.def,catchall:void 0}),t.extend=r=>ka(t,r),t.merge=r=>xa(t,r),t.pick=r=>ba(t,r),t.omit=r=>wa(t,r),t.partial=(...r)=>Ia(_n,t,r[0]),t.required=(...r)=>Sa(bn,t,r[0])});function N(t,e){const r={type:"object",get shape(){return Fe(this,"shape",{...t}),this.shape},...T(e)};return new Rt(r)}function V(t,e){return new Rt({type:"object",get shape(){return Fe(this,"shape",{...t}),this.shape},catchall:te(),...T(e)})}function Ie(t,e){return new Rt({type:"object",get shape(){return Fe(this,"shape",{...t}),this.shape},catchall:B(),...T(e)})}const yn=f("ZodUnion",(t,e)=>{un.init(t,e),W.init(t,e),t.options=e.options});function Q(t,e){return new yn({type:"union",options:t,...T(e)})}const Vo=f("ZodDiscriminatedUnion",(t,e)=>{yn.init(t,e),Xs.init(t,e)});function Bo(t,e,r){return new Vo({type:"union",options:e,discriminator:t,...T(r)})}const Jo=f("ZodIntersection",(t,e)=>{Qs.init(t,e),W.init(t,e)});function Wo(t,e){return new Jo({type:"intersection",left:t,right:e})}const qo=f("ZodRecord",(t,e)=>{ei.init(t,e),W.init(t,e),t.keyType=e.keyType,t.valueType=e.valueType});function kt(t,e,r){return new qo({type:"record",keyType:t,valueType:e,...T(r)})}const xt=f("ZodEnum",(t,e)=>{ti.init(t,e),W.init(t,e),t.enum=e.entries,t.options=Object.values(e.entries);const r=new Set(Object.keys(e.entries));t.extract=(n,a)=>{const s={};for(const o of n)if(r.has(o))s[o]=e.entries[o];else throw new Error(`Key ${o} not found in enum`);return new xt({...e,checks:[],...T(a),entries:s})},t.exclude=(n,a)=>{const s={...e.entries};for(const o of n)if(r.has(o))delete s[o];else throw new Error(`Key ${o} not found in enum`);return new xt({...e,checks:[],...T(a),entries:s})}});function lt(t,e){const r=Array.isArray(t)?Object.fromEntries(t.map(n=>[n,n])):t;return new xt({type:"enum",entries:r,...T(e)})}const Yo=f("ZodLiteral",(t,e)=>{ri.init(t,e),W.init(t,e),t.values=new Set(e.values),Object.defineProperty(t,"value",{get(){if(e.values.length>1)throw new Error("This schema contains multiple valid literal values. Use `.values` instead.");return e.values[0]}})});function I(t,e){return new Yo({type:"literal",values:Array.isArray(t)?t:[t],...T(e)})}const Ko=f("ZodTransform",(t,e)=>{ni.init(t,e),W.init(t,e),t._zod.parse=(r,n)=>{r.addIssue=s=>{if(typeof s=="string")r.issues.push(Re(s,r.value,e));else{const o=s;o.fatal&&(o.continue=!1),o.code??(o.code="custom"),o.input??(o.input=r.value),o.inst??(o.inst=t),o.continue??(o.continue=!0),r.issues.push(Re(o))}};const a=e.transform(r.value,r);return a instanceof Promise?a.then(s=>(r.value=s,r)):(r.value=a,r)}});function Go(t){return new Ko({type:"transform",transform:t})}const _n=f("ZodOptional",(t,e)=>{ai.init(t,e),W.init(t,e),t.unwrap=()=>t._zod.def.innerType});function G(t){return new _n({type:"optional",innerType:t})}const Ho=f("ZodNullable",(t,e)=>{si.init(t,e),W.init(t,e),t.unwrap=()=>t._zod.def.innerType});function or(t){return new Ho({type:"nullable",innerType:t})}const Xo=f("ZodDefault",(t,e)=>{ii.init(t,e),W.init(t,e),t.unwrap=()=>t._zod.def.innerType,t.removeDefault=t.unwrap});function Qo(t,e){return new Xo({type:"default",innerType:t,get defaultValue(){return typeof e=="function"?e():e}})}const eu=f("ZodPrefault",(t,e)=>{oi.init(t,e),W.init(t,e),t.unwrap=()=>t._zod.def.innerType});function tu(t,e){return new eu({type:"prefault",innerType:t,get defaultValue(){return typeof e=="function"?e():e}})}const bn=f("ZodNonOptional",(t,e)=>{ui.init(t,e),W.init(t,e),t.unwrap=()=>t._zod.def.innerType});function ru(t,e){return new bn({type:"nonoptional",innerType:t,...T(e)})}const nu=f("ZodCatch",(t,e)=>{ci.init(t,e),W.init(t,e),t.unwrap=()=>t._zod.def.innerType,t.removeCatch=t.unwrap});function au(t,e){return new nu({type:"catch",innerType:t,catchValue:typeof e=="function"?e:()=>e})}const su=f("ZodPipe",(t,e)=>{li.init(t,e),W.init(t,e),t.in=e.in,t.out=e.out});function ur(t,e){return new su({type:"pipe",in:t,out:e})}const iu=f("ZodReadonly",(t,e)=>{di.init(t,e),W.init(t,e)});function ou(t){return new iu({type:"readonly",innerType:t})}const uu=f("ZodLazy",(t,e)=>{pi.init(t,e),W.init(t,e),t.unwrap=()=>t._zod.def.getter()});function cu(t){return new uu({type:"lazy",getter:t})}const Pt=f("ZodCustom",(t,e)=>{fi.init(t,e),W.init(t,e)});function lu(t){const e=new re({check:"custom"});return e._zod.check=t,e}function du(t,e){return no(Pt,t??(()=>!0),e)}function pu(t,e={}){return ao(Pt,t,e)}function fu(t){const e=lu(r=>(r.addIssue=n=>{if(typeof n=="string")r.issues.push(Re(n,r.value,e._zod.def));else{const a=n;a.fatal&&(a.continue=!1),a.code??(a.code="custom"),a.input??(a.input=r.value),a.inst??(a.inst=e),a.continue??(a.continue=!e._zod.def.abort),r.issues.push(Re(a))}},t(r.value,r)));return e}function Qe(t,e={error:`Input not instance of ${t.name}`}){const r=new Pt({type:"custom",check:"custom",fn:n=>n instanceof t,abort:!0,...T(e)});return r._zod.bag.Class=t,r}const hu=Symbol("Let zodToJsonSchema decide on which parser to use"),cr={name:void 0,$refStrategy:"root",basePath:["#"],effectStrategy:"input",pipeStrategy:"all",dateStrategy:"format:date-time",mapStrategy:"entries",removeAdditionalStrategy:"passthrough",definitionPath:"definitions",target:"jsonSchema7",strictUnions:!1,definitions:{},errorMessages:!1,markdownDescription:!1,patternStrategy:"escape",applyRegexFlags:!1,emailStrategy:"format:email",base64Strategy:"contentEncoding:base64",nameStrategy:"ref"},mu=t=>typeof t=="string"?{...cr,name:t}:{...cr,...t},gu=t=>{const e=mu(t),r=e.name!==void 0?[...e.basePath,e.definitionPath,e.name]:e.basePath;return{...e,currentPath:r,propertyPath:void 0,seen:new Map(Object.entries(e.definitions).map(([n,a])=>[a._def,{def:a._def,path:[...e.basePath,e.definitionPath,n],jsonSchema:void 0}]))}};function wn(t,e,r,n){n!=null&&n.errorMessages&&r&&(t.errorMessage={...t.errorMessage,[e]:r})}function U(t,e,r,n,a){t[e]=r,wn(t,e,n,a)}var j;(function(t){t.assertEqual=a=>{};function e(a){}t.assertIs=e;function r(a){throw new Error}t.assertNever=r,t.arrayToEnum=a=>{const s={};for(const o of a)s[o]=o;return s},t.getValidEnumValues=a=>{const s=t.objectKeys(a).filter(i=>typeof a[a[i]]!="number"),o={};for(const i of s)o[i]=a[i];return t.objectValues(o)},t.objectValues=a=>t.objectKeys(a).map(function(s){return a[s]}),t.objectKeys=typeof Object.keys=="function"?a=>Object.keys(a):a=>{const s=[];for(const o in a)Object.prototype.hasOwnProperty.call(a,o)&&s.push(o);return s},t.find=(a,s)=>{for(const o of a)if(s(o))return o},t.isInteger=typeof Number.isInteger=="function"?a=>Number.isInteger(a):a=>typeof a=="number"&&Number.isFinite(a)&&Math.floor(a)===a;function n(a,s=" | "){return a.map(o=>typeof o=="string"?`'${o}'`:o).join(s)}t.joinValues=n,t.jsonStringifyReplacer=(a,s)=>typeof s=="bigint"?s.toString():s})(j||(j={}));var lr;(function(t){t.mergeShapes=(e,r)=>({...e,...r})})(lr||(lr={}));const S=j.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),de=t=>{switch(typeof t){case"undefined":return S.undefined;case"string":return S.string;case"number":return Number.isNaN(t)?S.nan:S.number;case"boolean":return S.boolean;case"function":return S.function;case"bigint":return S.bigint;case"symbol":return S.symbol;case"object":return Array.isArray(t)?S.array:t===null?S.null:t.then&&typeof t.then=="function"&&t.catch&&typeof t.catch=="function"?S.promise:typeof Map<"u"&&t instanceof Map?S.map:typeof Set<"u"&&t instanceof Set?S.set:typeof Date<"u"&&t instanceof Date?S.date:S.object;default:return S.unknown}},_=j.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class ce extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=n=>{this.issues=[...this.issues,n]},this.addIssues=(n=[])=>{this.issues=[...this.issues,...n]};const r=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,r):this.__proto__=r,this.name="ZodError",this.issues=e}format(e){const r=e||function(s){return s.message},n={_errors:[]},a=s=>{for(const o of s.issues)if(o.code==="invalid_union")o.unionErrors.map(a);else if(o.code==="invalid_return_type")a(o.returnTypeError);else if(o.code==="invalid_arguments")a(o.argumentsError);else if(o.path.length===0)n._errors.push(r(o));else{let i=n,c=0;for(;c<o.path.length;){const u=o.path[c];c===o.path.length-1?(i[u]=i[u]||{_errors:[]},i[u]._errors.push(r(o))):i[u]=i[u]||{_errors:[]},i=i[u],c++}}};return a(this),n}static assert(e){if(!(e instanceof ce))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,j.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(e=r=>r.message){const r={},n=[];for(const a of this.issues)if(a.path.length>0){const s=a.path[0];r[s]=r[s]||[],r[s].push(e(a))}else n.push(e(a));return{formErrors:n,fieldErrors:r}}get formErrors(){return this.flatten()}}ce.create=t=>new ce(t);const It=(t,e)=>{let r;switch(t.code){case _.invalid_type:t.received===S.undefined?r="Required":r=`Expected ${t.expected}, received ${t.received}`;break;case _.invalid_literal:r=`Invalid literal value, expected ${JSON.stringify(t.expected,j.jsonStringifyReplacer)}`;break;case _.unrecognized_keys:r=`Unrecognized key(s) in object: ${j.joinValues(t.keys,", ")}`;break;case _.invalid_union:r="Invalid input";break;case _.invalid_union_discriminator:r=`Invalid discriminator value. Expected ${j.joinValues(t.options)}`;break;case _.invalid_enum_value:r=`Invalid enum value. Expected ${j.joinValues(t.options)}, received '${t.received}'`;break;case _.invalid_arguments:r="Invalid function arguments";break;case _.invalid_return_type:r="Invalid function return type";break;case _.invalid_date:r="Invalid date";break;case _.invalid_string:typeof t.validation=="object"?"includes"in t.validation?(r=`Invalid input: must include "${t.validation.includes}"`,typeof t.validation.position=="number"&&(r=`${r} at one or more positions greater than or equal to ${t.validation.position}`)):"startsWith"in t.validation?r=`Invalid input: must start with "${t.validation.startsWith}"`:"endsWith"in t.validation?r=`Invalid input: must end with "${t.validation.endsWith}"`:j.assertNever(t.validation):t.validation!=="regex"?r=`Invalid ${t.validation}`:r="Invalid";break;case _.too_small:t.type==="array"?r=`Array must contain ${t.exact?"exactly":t.inclusive?"at least":"more than"} ${t.minimum} element(s)`:t.type==="string"?r=`String must contain ${t.exact?"exactly":t.inclusive?"at least":"over"} ${t.minimum} character(s)`:t.type==="number"?r=`Number must be ${t.exact?"exactly equal to ":t.inclusive?"greater than or equal to ":"greater than "}${t.minimum}`:t.type==="bigint"?r=`Number must be ${t.exact?"exactly equal to ":t.inclusive?"greater than or equal to ":"greater than "}${t.minimum}`:t.type==="date"?r=`Date must be ${t.exact?"exactly equal to ":t.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(t.minimum))}`:r="Invalid input";break;case _.too_big:t.type==="array"?r=`Array must contain ${t.exact?"exactly":t.inclusive?"at most":"less than"} ${t.maximum} element(s)`:t.type==="string"?r=`String must contain ${t.exact?"exactly":t.inclusive?"at most":"under"} ${t.maximum} character(s)`:t.type==="number"?r=`Number must be ${t.exact?"exactly":t.inclusive?"less than or equal to":"less than"} ${t.maximum}`:t.type==="bigint"?r=`BigInt must be ${t.exact?"exactly":t.inclusive?"less than or equal to":"less than"} ${t.maximum}`:t.type==="date"?r=`Date must be ${t.exact?"exactly":t.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(t.maximum))}`:r="Invalid input";break;case _.custom:r="Invalid input";break;case _.invalid_intersection_types:r="Intersection results could not be merged";break;case _.not_multiple_of:r=`Number must be a multiple of ${t.multipleOf}`;break;case _.not_finite:r="Number must be finite";break;default:r=e.defaultError,j.assertNever(t)}return{message:r}};let vu=It;function yu(){return vu}const _u=t=>{const{data:e,path:r,errorMaps:n,issueData:a}=t,s=[...r,...a.path||[]],o={...a,path:s};if(a.message!==void 0)return{...a,path:s,message:a.message};let i="";const c=n.filter(u=>!!u).slice().reverse();for(const u of c)i=u(o,{data:e,defaultError:i}).message;return{...a,path:s,message:i}};function k(t,e){const r=yu(),n=_u({issueData:e,data:t.data,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,r,r===It?void 0:It].filter(a=>!!a)});t.common.issues.push(n)}class ne{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(e,r){const n=[];for(const a of r){if(a.status==="aborted")return C;a.status==="dirty"&&e.dirty(),n.push(a.value)}return{status:e.value,value:n}}static async mergeObjectAsync(e,r){const n=[];for(const a of r){const s=await a.key,o=await a.value;n.push({key:s,value:o})}return ne.mergeObjectSync(e,n)}static mergeObjectSync(e,r){const n={};for(const a of r){const{key:s,value:o}=a;if(s.status==="aborted"||o.status==="aborted")return C;s.status==="dirty"&&e.dirty(),o.status==="dirty"&&e.dirty(),s.value!=="__proto__"&&(typeof o.value<"u"||a.alwaysSet)&&(n[s.value]=o.value)}return{status:e.value,value:n}}}const C=Object.freeze({status:"aborted"}),ze=t=>({status:"dirty",value:t}),ae=t=>({status:"valid",value:t}),dr=t=>t.status==="aborted",pr=t=>t.status==="dirty",Se=t=>t.status==="valid",et=t=>typeof Promise<"u"&&t instanceof Promise;var E;(function(t){t.errToObj=e=>typeof e=="string"?{message:e}:e||{},t.toString=e=>typeof e=="string"?e:e==null?void 0:e.message})(E||(E={}));class me{constructor(e,r,n,a){this._cachedPath=[],this.parent=e,this.data=r,this._path=n,this._key=a}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const fr=(t,e)=>{if(Se(e))return{success:!0,data:e.value};if(!t.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const r=new ce(t.common.issues);return this._error=r,this._error}}};function R(t){if(!t)return{};const{errorMap:e,invalid_type_error:r,required_error:n,description:a}=t;if(e&&(r||n))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:a}:{errorMap:(o,i)=>{const{message:c}=t;return o.code==="invalid_enum_value"?{message:c??i.defaultError}:typeof i.data>"u"?{message:c??n??i.defaultError}:o.code!=="invalid_type"?{message:i.defaultError}:{message:c??r??i.defaultError}},description:a}}class M{get description(){return this._def.description}_getType(e){return de(e.data)}_getOrReturnCtx(e,r){return r||{common:e.parent.common,data:e.data,parsedType:de(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new ne,ctx:{common:e.parent.common,data:e.data,parsedType:de(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const r=this._parse(e);if(et(r))throw new Error("Synchronous parse encountered promise.");return r}_parseAsync(e){const r=this._parse(e);return Promise.resolve(r)}parse(e,r){const n=this.safeParse(e,r);if(n.success)return n.data;throw n.error}safeParse(e,r){const n={common:{issues:[],async:(r==null?void 0:r.async)??!1,contextualErrorMap:r==null?void 0:r.errorMap},path:(r==null?void 0:r.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:de(e)},a=this._parseSync({data:e,path:n.path,parent:n});return fr(n,a)}"~validate"(e){var n,a;const r={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:de(e)};if(!this["~standard"].async)try{const s=this._parseSync({data:e,path:[],parent:r});return Se(s)?{value:s.value}:{issues:r.common.issues}}catch(s){(a=(n=s==null?void 0:s.message)==null?void 0:n.toLowerCase())!=null&&a.includes("encountered")&&(this["~standard"].async=!0),r.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:r}).then(s=>Se(s)?{value:s.value}:{issues:r.common.issues})}async parseAsync(e,r){const n=await this.safeParseAsync(e,r);if(n.success)return n.data;throw n.error}async safeParseAsync(e,r){const n={common:{issues:[],contextualErrorMap:r==null?void 0:r.errorMap,async:!0},path:(r==null?void 0:r.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:de(e)},a=this._parse({data:e,path:n.path,parent:n}),s=await(et(a)?a:Promise.resolve(a));return fr(n,s)}refine(e,r){const n=a=>typeof r=="string"||typeof r>"u"?{message:r}:typeof r=="function"?r(a):r;return this._refinement((a,s)=>{const o=e(a),i=()=>s.addIssue({code:_.custom,...n(a)});return typeof Promise<"u"&&o instanceof Promise?o.then(c=>c?!0:(i(),!1)):o?!0:(i(),!1)})}refinement(e,r){return this._refinement((n,a)=>e(n)?!0:(a.addIssue(typeof r=="function"?r(n,a):r),!1))}_refinement(e){return new Ee({schema:this,typeName:b.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:r=>this["~validate"](r)}}optional(){return ue.create(this,this._def)}nullable(){return Ze.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return oe.create(this)}promise(){return at.create(this,this._def)}or(e){return rt.create([this,e],this._def)}and(e){return nt.create(this,e,this._def)}transform(e){return new Ee({...R(this._def),schema:this,typeName:b.ZodEffects,effect:{type:"transform",transform:e}})}default(e){const r=typeof e=="function"?e:()=>e;return new Tt({...R(this._def),innerType:this,defaultValue:r,typeName:b.ZodDefault})}brand(){return new Fu({typeName:b.ZodBranded,type:this,...R(this._def)})}catch(e){const r=typeof e=="function"?e:()=>e;return new Et({...R(this._def),innerType:this,catchValue:r,typeName:b.ZodCatch})}describe(e){const r=this.constructor;return new r({...this._def,description:e})}pipe(e){return Mt.create(this,e)}readonly(){return Zt.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const bu=/^c[^\s-]{8,}$/i,wu=/^[0-9a-z]+$/,ku=/^[0-9A-HJKMNP-TV-Z]{26}$/i,xu=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,Iu=/^[a-z0-9_-]{21}$/i,Su=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,Tu=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,Eu=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,Zu="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";let vt;const $u=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,Ou=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,zu=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,Au=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,Nu=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,Cu=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,kn="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",Ru=new RegExp(`^${kn}$`);function xn(t){let e="[0-5]\\d";t.precision?e=`${e}\\.\\d{${t.precision}}`:t.precision==null&&(e=`${e}(\\.\\d+)?`);const r=t.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${e})${r}`}function Pu(t){return new RegExp(`^${xn(t)}$`)}function Mu(t){let e=`${kn}T${xn(t)}`;const r=[];return r.push(t.local?"Z?":"Z"),t.offset&&r.push("([+-]\\d{2}:?\\d{2})"),e=`${e}(${r.join("|")})`,new RegExp(`^${e}$`)}function ju(t,e){return!!((e==="v4"||!e)&&$u.test(t)||(e==="v6"||!e)&&zu.test(t))}function Du(t,e){if(!Su.test(t))return!1;try{const[r]=t.split(".");if(!r)return!1;const n=r.replace(/-/g,"+").replace(/_/g,"/").padEnd(r.length+(4-r.length%4)%4,"="),a=JSON.parse(atob(n));return!(typeof a!="object"||a===null||"typ"in a&&(a==null?void 0:a.typ)!=="JWT"||!a.alg||e&&a.alg!==e)}catch{return!1}}function Lu(t,e){return!!((e==="v4"||!e)&&Ou.test(t)||(e==="v6"||!e)&&Au.test(t))}class _e extends M{_parse(e){if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==S.string){const s=this._getOrReturnCtx(e);return k(s,{code:_.invalid_type,expected:S.string,received:s.parsedType}),C}const n=new ne;let a;for(const s of this._def.checks)if(s.kind==="min")e.data.length<s.value&&(a=this._getOrReturnCtx(e,a),k(a,{code:_.too_small,minimum:s.value,type:"string",inclusive:!0,exact:!1,message:s.message}),n.dirty());else if(s.kind==="max")e.data.length>s.value&&(a=this._getOrReturnCtx(e,a),k(a,{code:_.too_big,maximum:s.value,type:"string",inclusive:!0,exact:!1,message:s.message}),n.dirty());else if(s.kind==="length"){const o=e.data.length>s.value,i=e.data.length<s.value;(o||i)&&(a=this._getOrReturnCtx(e,a),o?k(a,{code:_.too_big,maximum:s.value,type:"string",inclusive:!0,exact:!0,message:s.message}):i&&k(a,{code:_.too_small,minimum:s.value,type:"string",inclusive:!0,exact:!0,message:s.message}),n.dirty())}else if(s.kind==="email")Eu.test(e.data)||(a=this._getOrReturnCtx(e,a),k(a,{validation:"email",code:_.invalid_string,message:s.message}),n.dirty());else if(s.kind==="emoji")vt||(vt=new RegExp(Zu,"u")),vt.test(e.data)||(a=this._getOrReturnCtx(e,a),k(a,{validation:"emoji",code:_.invalid_string,message:s.message}),n.dirty());else if(s.kind==="uuid")xu.test(e.data)||(a=this._getOrReturnCtx(e,a),k(a,{validation:"uuid",code:_.invalid_string,message:s.message}),n.dirty());else if(s.kind==="nanoid")Iu.test(e.data)||(a=this._getOrReturnCtx(e,a),k(a,{validation:"nanoid",code:_.invalid_string,message:s.message}),n.dirty());else if(s.kind==="cuid")bu.test(e.data)||(a=this._getOrReturnCtx(e,a),k(a,{validation:"cuid",code:_.invalid_string,message:s.message}),n.dirty());else if(s.kind==="cuid2")wu.test(e.data)||(a=this._getOrReturnCtx(e,a),k(a,{validation:"cuid2",code:_.invalid_string,message:s.message}),n.dirty());else if(s.kind==="ulid")ku.test(e.data)||(a=this._getOrReturnCtx(e,a),k(a,{validation:"ulid",code:_.invalid_string,message:s.message}),n.dirty());else if(s.kind==="url")try{new URL(e.data)}catch{a=this._getOrReturnCtx(e,a),k(a,{validation:"url",code:_.invalid_string,message:s.message}),n.dirty()}else s.kind==="regex"?(s.regex.lastIndex=0,s.regex.test(e.data)||(a=this._getOrReturnCtx(e,a),k(a,{validation:"regex",code:_.invalid_string,message:s.message}),n.dirty())):s.kind==="trim"?e.data=e.data.trim():s.kind==="includes"?e.data.includes(s.value,s.position)||(a=this._getOrReturnCtx(e,a),k(a,{code:_.invalid_string,validation:{includes:s.value,position:s.position},message:s.message}),n.dirty()):s.kind==="toLowerCase"?e.data=e.data.toLowerCase():s.kind==="toUpperCase"?e.data=e.data.toUpperCase():s.kind==="startsWith"?e.data.startsWith(s.value)||(a=this._getOrReturnCtx(e,a),k(a,{code:_.invalid_string,validation:{startsWith:s.value},message:s.message}),n.dirty()):s.kind==="endsWith"?e.data.endsWith(s.value)||(a=this._getOrReturnCtx(e,a),k(a,{code:_.invalid_string,validation:{endsWith:s.value},message:s.message}),n.dirty()):s.kind==="datetime"?Mu(s).test(e.data)||(a=this._getOrReturnCtx(e,a),k(a,{code:_.invalid_string,validation:"datetime",message:s.message}),n.dirty()):s.kind==="date"?Ru.test(e.data)||(a=this._getOrReturnCtx(e,a),k(a,{code:_.invalid_string,validation:"date",message:s.message}),n.dirty()):s.kind==="time"?Pu(s).test(e.data)||(a=this._getOrReturnCtx(e,a),k(a,{code:_.invalid_string,validation:"time",message:s.message}),n.dirty()):s.kind==="duration"?Tu.test(e.data)||(a=this._getOrReturnCtx(e,a),k(a,{validation:"duration",code:_.invalid_string,message:s.message}),n.dirty()):s.kind==="ip"?ju(e.data,s.version)||(a=this._getOrReturnCtx(e,a),k(a,{validation:"ip",code:_.invalid_string,message:s.message}),n.dirty()):s.kind==="jwt"?Du(e.data,s.alg)||(a=this._getOrReturnCtx(e,a),k(a,{validation:"jwt",code:_.invalid_string,message:s.message}),n.dirty()):s.kind==="cidr"?Lu(e.data,s.version)||(a=this._getOrReturnCtx(e,a),k(a,{validation:"cidr",code:_.invalid_string,message:s.message}),n.dirty()):s.kind==="base64"?Nu.test(e.data)||(a=this._getOrReturnCtx(e,a),k(a,{validation:"base64",code:_.invalid_string,message:s.message}),n.dirty()):s.kind==="base64url"?Cu.test(e.data)||(a=this._getOrReturnCtx(e,a),k(a,{validation:"base64url",code:_.invalid_string,message:s.message}),n.dirty()):j.assertNever(s);return{status:n.value,value:e.data}}_regex(e,r,n){return this.refinement(a=>e.test(a),{validation:r,code:_.invalid_string,...E.errToObj(n)})}_addCheck(e){return new _e({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...E.errToObj(e)})}url(e){return this._addCheck({kind:"url",...E.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...E.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...E.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...E.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...E.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...E.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...E.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...E.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...E.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...E.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...E.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...E.errToObj(e)})}datetime(e){return typeof e=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:typeof(e==null?void 0:e.precision)>"u"?null:e==null?void 0:e.precision,offset:(e==null?void 0:e.offset)??!1,local:(e==null?void 0:e.local)??!1,...E.errToObj(e==null?void 0:e.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return typeof e=="string"?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:typeof(e==null?void 0:e.precision)>"u"?null:e==null?void 0:e.precision,...E.errToObj(e==null?void 0:e.message)})}duration(e){return this._addCheck({kind:"duration",...E.errToObj(e)})}regex(e,r){return this._addCheck({kind:"regex",regex:e,...E.errToObj(r)})}includes(e,r){return this._addCheck({kind:"includes",value:e,position:r==null?void 0:r.position,...E.errToObj(r==null?void 0:r.message)})}startsWith(e,r){return this._addCheck({kind:"startsWith",value:e,...E.errToObj(r)})}endsWith(e,r){return this._addCheck({kind:"endsWith",value:e,...E.errToObj(r)})}min(e,r){return this._addCheck({kind:"min",value:e,...E.errToObj(r)})}max(e,r){return this._addCheck({kind:"max",value:e,...E.errToObj(r)})}length(e,r){return this._addCheck({kind:"length",value:e,...E.errToObj(r)})}nonempty(e){return this.min(1,E.errToObj(e))}trim(){return new _e({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new _e({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new _e({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>e.kind==="datetime")}get isDate(){return!!this._def.checks.find(e=>e.kind==="date")}get isTime(){return!!this._def.checks.find(e=>e.kind==="time")}get isDuration(){return!!this._def.checks.find(e=>e.kind==="duration")}get isEmail(){return!!this._def.checks.find(e=>e.kind==="email")}get isURL(){return!!this._def.checks.find(e=>e.kind==="url")}get isEmoji(){return!!this._def.checks.find(e=>e.kind==="emoji")}get isUUID(){return!!this._def.checks.find(e=>e.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(e=>e.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(e=>e.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(e=>e.kind==="cuid2")}get isULID(){return!!this._def.checks.find(e=>e.kind==="ulid")}get isIP(){return!!this._def.checks.find(e=>e.kind==="ip")}get isCIDR(){return!!this._def.checks.find(e=>e.kind==="cidr")}get isBase64(){return!!this._def.checks.find(e=>e.kind==="base64")}get isBase64url(){return!!this._def.checks.find(e=>e.kind==="base64url")}get minLength(){let e=null;for(const r of this._def.checks)r.kind==="min"&&(e===null||r.value>e)&&(e=r.value);return e}get maxLength(){let e=null;for(const r of this._def.checks)r.kind==="max"&&(e===null||r.value<e)&&(e=r.value);return e}}_e.create=t=>new _e({checks:[],typeName:b.ZodString,coerce:(t==null?void 0:t.coerce)??!1,...R(t)});function Uu(t,e){const r=(t.toString().split(".")[1]||"").length,n=(e.toString().split(".")[1]||"").length,a=r>n?r:n,s=Number.parseInt(t.toFixed(a).replace(".","")),o=Number.parseInt(e.toFixed(a).replace(".",""));return s%o/10**a}class Me extends M{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==S.number){const s=this._getOrReturnCtx(e);return k(s,{code:_.invalid_type,expected:S.number,received:s.parsedType}),C}let n;const a=new ne;for(const s of this._def.checks)s.kind==="int"?j.isInteger(e.data)||(n=this._getOrReturnCtx(e,n),k(n,{code:_.invalid_type,expected:"integer",received:"float",message:s.message}),a.dirty()):s.kind==="min"?(s.inclusive?e.data<s.value:e.data<=s.value)&&(n=this._getOrReturnCtx(e,n),k(n,{code:_.too_small,minimum:s.value,type:"number",inclusive:s.inclusive,exact:!1,message:s.message}),a.dirty()):s.kind==="max"?(s.inclusive?e.data>s.value:e.data>=s.value)&&(n=this._getOrReturnCtx(e,n),k(n,{code:_.too_big,maximum:s.value,type:"number",inclusive:s.inclusive,exact:!1,message:s.message}),a.dirty()):s.kind==="multipleOf"?Uu(e.data,s.value)!==0&&(n=this._getOrReturnCtx(e,n),k(n,{code:_.not_multiple_of,multipleOf:s.value,message:s.message}),a.dirty()):s.kind==="finite"?Number.isFinite(e.data)||(n=this._getOrReturnCtx(e,n),k(n,{code:_.not_finite,message:s.message}),a.dirty()):j.assertNever(s);return{status:a.value,value:e.data}}gte(e,r){return this.setLimit("min",e,!0,E.toString(r))}gt(e,r){return this.setLimit("min",e,!1,E.toString(r))}lte(e,r){return this.setLimit("max",e,!0,E.toString(r))}lt(e,r){return this.setLimit("max",e,!1,E.toString(r))}setLimit(e,r,n,a){return new Me({...this._def,checks:[...this._def.checks,{kind:e,value:r,inclusive:n,message:E.toString(a)}]})}_addCheck(e){return new Me({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:E.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:E.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:E.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:E.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:E.toString(e)})}multipleOf(e,r){return this._addCheck({kind:"multipleOf",value:e,message:E.toString(r)})}finite(e){return this._addCheck({kind:"finite",message:E.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:E.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:E.toString(e)})}get minValue(){let e=null;for(const r of this._def.checks)r.kind==="min"&&(e===null||r.value>e)&&(e=r.value);return e}get maxValue(){let e=null;for(const r of this._def.checks)r.kind==="max"&&(e===null||r.value<e)&&(e=r.value);return e}get isInt(){return!!this._def.checks.find(e=>e.kind==="int"||e.kind==="multipleOf"&&j.isInteger(e.value))}get isFinite(){let e=null,r=null;for(const n of this._def.checks){if(n.kind==="finite"||n.kind==="int"||n.kind==="multipleOf")return!0;n.kind==="min"?(r===null||n.value>r)&&(r=n.value):n.kind==="max"&&(e===null||n.value<e)&&(e=n.value)}return Number.isFinite(r)&&Number.isFinite(e)}}Me.create=t=>new Me({checks:[],typeName:b.ZodNumber,coerce:(t==null?void 0:t.coerce)||!1,...R(t)});class je extends M{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==S.bigint)return this._getInvalidInput(e);let n;const a=new ne;for(const s of this._def.checks)s.kind==="min"?(s.inclusive?e.data<s.value:e.data<=s.value)&&(n=this._getOrReturnCtx(e,n),k(n,{code:_.too_small,type:"bigint",minimum:s.value,inclusive:s.inclusive,message:s.message}),a.dirty()):s.kind==="max"?(s.inclusive?e.data>s.value:e.data>=s.value)&&(n=this._getOrReturnCtx(e,n),k(n,{code:_.too_big,type:"bigint",maximum:s.value,inclusive:s.inclusive,message:s.message}),a.dirty()):s.kind==="multipleOf"?e.data%s.value!==BigInt(0)&&(n=this._getOrReturnCtx(e,n),k(n,{code:_.not_multiple_of,multipleOf:s.value,message:s.message}),a.dirty()):j.assertNever(s);return{status:a.value,value:e.data}}_getInvalidInput(e){const r=this._getOrReturnCtx(e);return k(r,{code:_.invalid_type,expected:S.bigint,received:r.parsedType}),C}gte(e,r){return this.setLimit("min",e,!0,E.toString(r))}gt(e,r){return this.setLimit("min",e,!1,E.toString(r))}lte(e,r){return this.setLimit("max",e,!0,E.toString(r))}lt(e,r){return this.setLimit("max",e,!1,E.toString(r))}setLimit(e,r,n,a){return new je({...this._def,checks:[...this._def.checks,{kind:e,value:r,inclusive:n,message:E.toString(a)}]})}_addCheck(e){return new je({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:E.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:E.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:E.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:E.toString(e)})}multipleOf(e,r){return this._addCheck({kind:"multipleOf",value:e,message:E.toString(r)})}get minValue(){let e=null;for(const r of this._def.checks)r.kind==="min"&&(e===null||r.value>e)&&(e=r.value);return e}get maxValue(){let e=null;for(const r of this._def.checks)r.kind==="max"&&(e===null||r.value<e)&&(e=r.value);return e}}je.create=t=>new je({checks:[],typeName:b.ZodBigInt,coerce:(t==null?void 0:t.coerce)??!1,...R(t)});class hr extends M{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==S.boolean){const n=this._getOrReturnCtx(e);return k(n,{code:_.invalid_type,expected:S.boolean,received:n.parsedType}),C}return ae(e.data)}}hr.create=t=>new hr({typeName:b.ZodBoolean,coerce:(t==null?void 0:t.coerce)||!1,...R(t)});class tt extends M{_parse(e){if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==S.date){const s=this._getOrReturnCtx(e);return k(s,{code:_.invalid_type,expected:S.date,received:s.parsedType}),C}if(Number.isNaN(e.data.getTime())){const s=this._getOrReturnCtx(e);return k(s,{code:_.invalid_date}),C}const n=new ne;let a;for(const s of this._def.checks)s.kind==="min"?e.data.getTime()<s.value&&(a=this._getOrReturnCtx(e,a),k(a,{code:_.too_small,message:s.message,inclusive:!0,exact:!1,minimum:s.value,type:"date"}),n.dirty()):s.kind==="max"?e.data.getTime()>s.value&&(a=this._getOrReturnCtx(e,a),k(a,{code:_.too_big,message:s.message,inclusive:!0,exact:!1,maximum:s.value,type:"date"}),n.dirty()):j.assertNever(s);return{status:n.value,value:new Date(e.data.getTime())}}_addCheck(e){return new tt({...this._def,checks:[...this._def.checks,e]})}min(e,r){return this._addCheck({kind:"min",value:e.getTime(),message:E.toString(r)})}max(e,r){return this._addCheck({kind:"max",value:e.getTime(),message:E.toString(r)})}get minDate(){let e=null;for(const r of this._def.checks)r.kind==="min"&&(e===null||r.value>e)&&(e=r.value);return e!=null?new Date(e):null}get maxDate(){let e=null;for(const r of this._def.checks)r.kind==="max"&&(e===null||r.value<e)&&(e=r.value);return e!=null?new Date(e):null}}tt.create=t=>new tt({checks:[],coerce:(t==null?void 0:t.coerce)||!1,typeName:b.ZodDate,...R(t)});class mr extends M{_parse(e){if(this._getType(e)!==S.symbol){const n=this._getOrReturnCtx(e);return k(n,{code:_.invalid_type,expected:S.symbol,received:n.parsedType}),C}return ae(e.data)}}mr.create=t=>new mr({typeName:b.ZodSymbol,...R(t)});class gr extends M{_parse(e){if(this._getType(e)!==S.undefined){const n=this._getOrReturnCtx(e);return k(n,{code:_.invalid_type,expected:S.undefined,received:n.parsedType}),C}return ae(e.data)}}gr.create=t=>new gr({typeName:b.ZodUndefined,...R(t)});class vr extends M{_parse(e){if(this._getType(e)!==S.null){const n=this._getOrReturnCtx(e);return k(n,{code:_.invalid_type,expected:S.null,received:n.parsedType}),C}return ae(e.data)}}vr.create=t=>new vr({typeName:b.ZodNull,...R(t)});class yr extends M{constructor(){super(...arguments),this._any=!0}_parse(e){return ae(e.data)}}yr.create=t=>new yr({typeName:b.ZodAny,...R(t)});class _r extends M{constructor(){super(...arguments),this._unknown=!0}_parse(e){return ae(e.data)}}_r.create=t=>new _r({typeName:b.ZodUnknown,...R(t)});class ge extends M{_parse(e){const r=this._getOrReturnCtx(e);return k(r,{code:_.invalid_type,expected:S.never,received:r.parsedType}),C}}ge.create=t=>new ge({typeName:b.ZodNever,...R(t)});class br extends M{_parse(e){if(this._getType(e)!==S.undefined){const n=this._getOrReturnCtx(e);return k(n,{code:_.invalid_type,expected:S.void,received:n.parsedType}),C}return ae(e.data)}}br.create=t=>new br({typeName:b.ZodVoid,...R(t)});class oe extends M{_parse(e){const{ctx:r,status:n}=this._processInputParams(e),a=this._def;if(r.parsedType!==S.array)return k(r,{code:_.invalid_type,expected:S.array,received:r.parsedType}),C;if(a.exactLength!==null){const o=r.data.length>a.exactLength.value,i=r.data.length<a.exactLength.value;(o||i)&&(k(r,{code:o?_.too_big:_.too_small,minimum:i?a.exactLength.value:void 0,maximum:o?a.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:a.exactLength.message}),n.dirty())}if(a.minLength!==null&&r.data.length<a.minLength.value&&(k(r,{code:_.too_small,minimum:a.minLength.value,type:"array",inclusive:!0,exact:!1,message:a.minLength.message}),n.dirty()),a.maxLength!==null&&r.data.length>a.maxLength.value&&(k(r,{code:_.too_big,maximum:a.maxLength.value,type:"array",inclusive:!0,exact:!1,message:a.maxLength.message}),n.dirty()),r.common.async)return Promise.all([...r.data].map((o,i)=>a.type._parseAsync(new me(r,o,r.path,i)))).then(o=>ne.mergeArray(n,o));const s=[...r.data].map((o,i)=>a.type._parseSync(new me(r,o,r.path,i)));return ne.mergeArray(n,s)}get element(){return this._def.type}min(e,r){return new oe({...this._def,minLength:{value:e,message:E.toString(r)}})}max(e,r){return new oe({...this._def,maxLength:{value:e,message:E.toString(r)}})}length(e,r){return new oe({...this._def,exactLength:{value:e,message:E.toString(r)}})}nonempty(e){return this.min(1,e)}}oe.create=(t,e)=>new oe({type:t,minLength:null,maxLength:null,exactLength:null,typeName:b.ZodArray,...R(e)});function xe(t){if(t instanceof X){const e={};for(const r in t.shape){const n=t.shape[r];e[r]=ue.create(xe(n))}return new X({...t._def,shape:()=>e})}else return t instanceof oe?new oe({...t._def,type:xe(t.element)}):t instanceof ue?ue.create(xe(t.unwrap())):t instanceof Ze?Ze.create(xe(t.unwrap())):t instanceof be?be.create(t.items.map(e=>xe(e))):t}class X extends M{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const e=this._def.shape(),r=j.objectKeys(e);return this._cached={shape:e,keys:r},this._cached}_parse(e){if(this._getType(e)!==S.object){const u=this._getOrReturnCtx(e);return k(u,{code:_.invalid_type,expected:S.object,received:u.parsedType}),C}const{status:n,ctx:a}=this._processInputParams(e),{shape:s,keys:o}=this._getCached(),i=[];if(!(this._def.catchall instanceof ge&&this._def.unknownKeys==="strip"))for(const u in a.data)o.includes(u)||i.push(u);const c=[];for(const u of o){const d=s[u],y=a.data[u];c.push({key:{status:"valid",value:u},value:d._parse(new me(a,y,a.path,u)),alwaysSet:u in a.data})}if(this._def.catchall instanceof ge){const u=this._def.unknownKeys;if(u==="passthrough")for(const d of i)c.push({key:{status:"valid",value:d},value:{status:"valid",value:a.data[d]}});else if(u==="strict")i.length>0&&(k(a,{code:_.unrecognized_keys,keys:i}),n.dirty());else if(u!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const u=this._def.catchall;for(const d of i){const y=a.data[d];c.push({key:{status:"valid",value:d},value:u._parse(new me(a,y,a.path,d)),alwaysSet:d in a.data})}}return a.common.async?Promise.resolve().then(async()=>{const u=[];for(const d of c){const y=await d.key,x=await d.value;u.push({key:y,value:x,alwaysSet:d.alwaysSet})}return u}).then(u=>ne.mergeObjectSync(n,u)):ne.mergeObjectSync(n,c)}get shape(){return this._def.shape()}strict(e){return E.errToObj,new X({...this._def,unknownKeys:"strict",...e!==void 0?{errorMap:(r,n)=>{var s,o;const a=((o=(s=this._def).errorMap)==null?void 0:o.call(s,r,n).message)??n.defaultError;return r.code==="unrecognized_keys"?{message:E.errToObj(e).message??a}:{message:a}}}:{}})}strip(){return new X({...this._def,unknownKeys:"strip"})}passthrough(){return new X({...this._def,unknownKeys:"passthrough"})}extend(e){return new X({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new X({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:b.ZodObject})}setKey(e,r){return this.augment({[e]:r})}catchall(e){return new X({...this._def,catchall:e})}pick(e){const r={};for(const n of j.objectKeys(e))e[n]&&this.shape[n]&&(r[n]=this.shape[n]);return new X({...this._def,shape:()=>r})}omit(e){const r={};for(const n of j.objectKeys(this.shape))e[n]||(r[n]=this.shape[n]);return new X({...this._def,shape:()=>r})}deepPartial(){return xe(this)}partial(e){const r={};for(const n of j.objectKeys(this.shape)){const a=this.shape[n];e&&!e[n]?r[n]=a:r[n]=a.optional()}return new X({...this._def,shape:()=>r})}required(e){const r={};for(const n of j.objectKeys(this.shape))if(e&&!e[n])r[n]=this.shape[n];else{let s=this.shape[n];for(;s instanceof ue;)s=s._def.innerType;r[n]=s}return new X({...this._def,shape:()=>r})}keyof(){return In(j.objectKeys(this.shape))}}X.create=(t,e)=>new X({shape:()=>t,unknownKeys:"strip",catchall:ge.create(),typeName:b.ZodObject,...R(e)});X.strictCreate=(t,e)=>new X({shape:()=>t,unknownKeys:"strict",catchall:ge.create(),typeName:b.ZodObject,...R(e)});X.lazycreate=(t,e)=>new X({shape:t,unknownKeys:"strip",catchall:ge.create(),typeName:b.ZodObject,...R(e)});class rt extends M{_parse(e){const{ctx:r}=this._processInputParams(e),n=this._def.options;function a(s){for(const i of s)if(i.result.status==="valid")return i.result;for(const i of s)if(i.result.status==="dirty")return r.common.issues.push(...i.ctx.common.issues),i.result;const o=s.map(i=>new ce(i.ctx.common.issues));return k(r,{code:_.invalid_union,unionErrors:o}),C}if(r.common.async)return Promise.all(n.map(async s=>{const o={...r,common:{...r.common,issues:[]},parent:null};return{result:await s._parseAsync({data:r.data,path:r.path,parent:o}),ctx:o}})).then(a);{let s;const o=[];for(const c of n){const u={...r,common:{...r.common,issues:[]},parent:null},d=c._parseSync({data:r.data,path:r.path,parent:u});if(d.status==="valid")return d;d.status==="dirty"&&!s&&(s={result:d,ctx:u}),u.common.issues.length&&o.push(u.common.issues)}if(s)return r.common.issues.push(...s.ctx.common.issues),s.result;const i=o.map(c=>new ce(c));return k(r,{code:_.invalid_union,unionErrors:i}),C}}get options(){return this._def.options}}rt.create=(t,e)=>new rt({options:t,typeName:b.ZodUnion,...R(e)});function St(t,e){const r=de(t),n=de(e);if(t===e)return{valid:!0,data:t};if(r===S.object&&n===S.object){const a=j.objectKeys(e),s=j.objectKeys(t).filter(i=>a.indexOf(i)!==-1),o={...t,...e};for(const i of s){const c=St(t[i],e[i]);if(!c.valid)return{valid:!1};o[i]=c.data}return{valid:!0,data:o}}else if(r===S.array&&n===S.array){if(t.length!==e.length)return{valid:!1};const a=[];for(let s=0;s<t.length;s++){const o=t[s],i=e[s],c=St(o,i);if(!c.valid)return{valid:!1};a.push(c.data)}return{valid:!0,data:a}}else return r===S.date&&n===S.date&&+t==+e?{valid:!0,data:t}:{valid:!1}}class nt extends M{_parse(e){const{status:r,ctx:n}=this._processInputParams(e),a=(s,o)=>{if(dr(s)||dr(o))return C;const i=St(s.value,o.value);return i.valid?((pr(s)||pr(o))&&r.dirty(),{status:r.value,value:i.data}):(k(n,{code:_.invalid_intersection_types}),C)};return n.common.async?Promise.all([this._def.left._parseAsync({data:n.data,path:n.path,parent:n}),this._def.right._parseAsync({data:n.data,path:n.path,parent:n})]).then(([s,o])=>a(s,o)):a(this._def.left._parseSync({data:n.data,path:n.path,parent:n}),this._def.right._parseSync({data:n.data,path:n.path,parent:n}))}}nt.create=(t,e,r)=>new nt({left:t,right:e,typeName:b.ZodIntersection,...R(r)});class be extends M{_parse(e){const{status:r,ctx:n}=this._processInputParams(e);if(n.parsedType!==S.array)return k(n,{code:_.invalid_type,expected:S.array,received:n.parsedType}),C;if(n.data.length<this._def.items.length)return k(n,{code:_.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),C;!this._def.rest&&n.data.length>this._def.items.length&&(k(n,{code:_.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),r.dirty());const s=[...n.data].map((o,i)=>{const c=this._def.items[i]||this._def.rest;return c?c._parse(new me(n,o,n.path,i)):null}).filter(o=>!!o);return n.common.async?Promise.all(s).then(o=>ne.mergeArray(r,o)):ne.mergeArray(r,s)}get items(){return this._def.items}rest(e){return new be({...this._def,rest:e})}}be.create=(t,e)=>{if(!Array.isArray(t))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new be({items:t,typeName:b.ZodTuple,rest:null,...R(e)})};class wr extends M{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:r,ctx:n}=this._processInputParams(e);if(n.parsedType!==S.map)return k(n,{code:_.invalid_type,expected:S.map,received:n.parsedType}),C;const a=this._def.keyType,s=this._def.valueType,o=[...n.data.entries()].map(([i,c],u)=>({key:a._parse(new me(n,i,n.path,[u,"key"])),value:s._parse(new me(n,c,n.path,[u,"value"]))}));if(n.common.async){const i=new Map;return Promise.resolve().then(async()=>{for(const c of o){const u=await c.key,d=await c.value;if(u.status==="aborted"||d.status==="aborted")return C;(u.status==="dirty"||d.status==="dirty")&&r.dirty(),i.set(u.value,d.value)}return{status:r.value,value:i}})}else{const i=new Map;for(const c of o){const u=c.key,d=c.value;if(u.status==="aborted"||d.status==="aborted")return C;(u.status==="dirty"||d.status==="dirty")&&r.dirty(),i.set(u.value,d.value)}return{status:r.value,value:i}}}}wr.create=(t,e,r)=>new wr({valueType:e,keyType:t,typeName:b.ZodMap,...R(r)});class De extends M{_parse(e){const{status:r,ctx:n}=this._processInputParams(e);if(n.parsedType!==S.set)return k(n,{code:_.invalid_type,expected:S.set,received:n.parsedType}),C;const a=this._def;a.minSize!==null&&n.data.size<a.minSize.value&&(k(n,{code:_.too_small,minimum:a.minSize.value,type:"set",inclusive:!0,exact:!1,message:a.minSize.message}),r.dirty()),a.maxSize!==null&&n.data.size>a.maxSize.value&&(k(n,{code:_.too_big,maximum:a.maxSize.value,type:"set",inclusive:!0,exact:!1,message:a.maxSize.message}),r.dirty());const s=this._def.valueType;function o(c){const u=new Set;for(const d of c){if(d.status==="aborted")return C;d.status==="dirty"&&r.dirty(),u.add(d.value)}return{status:r.value,value:u}}const i=[...n.data.values()].map((c,u)=>s._parse(new me(n,c,n.path,u)));return n.common.async?Promise.all(i).then(c=>o(c)):o(i)}min(e,r){return new De({...this._def,minSize:{value:e,message:E.toString(r)}})}max(e,r){return new De({...this._def,maxSize:{value:e,message:E.toString(r)}})}size(e,r){return this.min(e,r).max(e,r)}nonempty(e){return this.min(1,e)}}De.create=(t,e)=>new De({valueType:t,minSize:null,maxSize:null,typeName:b.ZodSet,...R(e)});class kr extends M{get schema(){return this._def.getter()}_parse(e){const{ctx:r}=this._processInputParams(e);return this._def.getter()._parse({data:r.data,path:r.path,parent:r})}}kr.create=(t,e)=>new kr({getter:t,typeName:b.ZodLazy,...R(e)});class xr extends M{_parse(e){if(e.data!==this._def.value){const r=this._getOrReturnCtx(e);return k(r,{received:r.data,code:_.invalid_literal,expected:this._def.value}),C}return{status:"valid",value:e.data}}get value(){return this._def.value}}xr.create=(t,e)=>new xr({value:t,typeName:b.ZodLiteral,...R(e)});function In(t,e){return new Te({values:t,typeName:b.ZodEnum,...R(e)})}class Te extends M{_parse(e){if(typeof e.data!="string"){const r=this._getOrReturnCtx(e),n=this._def.values;return k(r,{expected:j.joinValues(n),received:r.parsedType,code:_.invalid_type}),C}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(e.data)){const r=this._getOrReturnCtx(e),n=this._def.values;return k(r,{received:r.data,code:_.invalid_enum_value,options:n}),C}return ae(e.data)}get options(){return this._def.values}get enum(){const e={};for(const r of this._def.values)e[r]=r;return e}get Values(){const e={};for(const r of this._def.values)e[r]=r;return e}get Enum(){const e={};for(const r of this._def.values)e[r]=r;return e}extract(e,r=this._def){return Te.create(e,{...this._def,...r})}exclude(e,r=this._def){return Te.create(this.options.filter(n=>!e.includes(n)),{...this._def,...r})}}Te.create=In;class Ir extends M{_parse(e){const r=j.getValidEnumValues(this._def.values),n=this._getOrReturnCtx(e);if(n.parsedType!==S.string&&n.parsedType!==S.number){const a=j.objectValues(r);return k(n,{expected:j.joinValues(a),received:n.parsedType,code:_.invalid_type}),C}if(this._cache||(this._cache=new Set(j.getValidEnumValues(this._def.values))),!this._cache.has(e.data)){const a=j.objectValues(r);return k(n,{received:n.data,code:_.invalid_enum_value,options:a}),C}return ae(e.data)}get enum(){return this._def.values}}Ir.create=(t,e)=>new Ir({values:t,typeName:b.ZodNativeEnum,...R(e)});class at extends M{unwrap(){return this._def.type}_parse(e){const{ctx:r}=this._processInputParams(e);if(r.parsedType!==S.promise&&r.common.async===!1)return k(r,{code:_.invalid_type,expected:S.promise,received:r.parsedType}),C;const n=r.parsedType===S.promise?r.data:Promise.resolve(r.data);return ae(n.then(a=>this._def.type.parseAsync(a,{path:r.path,errorMap:r.common.contextualErrorMap})))}}at.create=(t,e)=>new at({type:t,typeName:b.ZodPromise,...R(e)});class Ee extends M{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===b.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:r,ctx:n}=this._processInputParams(e),a=this._def.effect||null,s={addIssue:o=>{k(n,o),o.fatal?r.abort():r.dirty()},get path(){return n.path}};if(s.addIssue=s.addIssue.bind(s),a.type==="preprocess"){const o=a.transform(n.data,s);if(n.common.async)return Promise.resolve(o).then(async i=>{if(r.value==="aborted")return C;const c=await this._def.schema._parseAsync({data:i,path:n.path,parent:n});return c.status==="aborted"?C:c.status==="dirty"||r.value==="dirty"?ze(c.value):c});{if(r.value==="aborted")return C;const i=this._def.schema._parseSync({data:o,path:n.path,parent:n});return i.status==="aborted"?C:i.status==="dirty"||r.value==="dirty"?ze(i.value):i}}if(a.type==="refinement"){const o=i=>{const c=a.refinement(i,s);if(n.common.async)return Promise.resolve(c);if(c instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return i};if(n.common.async===!1){const i=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});return i.status==="aborted"?C:(i.status==="dirty"&&r.dirty(),o(i.value),{status:r.value,value:i.value})}else return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(i=>i.status==="aborted"?C:(i.status==="dirty"&&r.dirty(),o(i.value).then(()=>({status:r.value,value:i.value}))))}if(a.type==="transform")if(n.common.async===!1){const o=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});if(!Se(o))return C;const i=a.transform(o.value,s);if(i instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:r.value,value:i}}else return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(o=>Se(o)?Promise.resolve(a.transform(o.value,s)).then(i=>({status:r.value,value:i})):C);j.assertNever(a)}}Ee.create=(t,e,r)=>new Ee({schema:t,typeName:b.ZodEffects,effect:e,...R(r)});Ee.createWithPreprocess=(t,e,r)=>new Ee({schema:e,effect:{type:"preprocess",transform:t},typeName:b.ZodEffects,...R(r)});class ue extends M{_parse(e){return this._getType(e)===S.undefined?ae(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}ue.create=(t,e)=>new ue({innerType:t,typeName:b.ZodOptional,...R(e)});class Ze extends M{_parse(e){return this._getType(e)===S.null?ae(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}Ze.create=(t,e)=>new Ze({innerType:t,typeName:b.ZodNullable,...R(e)});class Tt extends M{_parse(e){const{ctx:r}=this._processInputParams(e);let n=r.data;return r.parsedType===S.undefined&&(n=this._def.defaultValue()),this._def.innerType._parse({data:n,path:r.path,parent:r})}removeDefault(){return this._def.innerType}}Tt.create=(t,e)=>new Tt({innerType:t,typeName:b.ZodDefault,defaultValue:typeof e.default=="function"?e.default:()=>e.default,...R(e)});class Et extends M{_parse(e){const{ctx:r}=this._processInputParams(e),n={...r,common:{...r.common,issues:[]}},a=this._def.innerType._parse({data:n.data,path:n.path,parent:{...n}});return et(a)?a.then(s=>({status:"valid",value:s.status==="valid"?s.value:this._def.catchValue({get error(){return new ce(n.common.issues)},input:n.data})})):{status:"valid",value:a.status==="valid"?a.value:this._def.catchValue({get error(){return new ce(n.common.issues)},input:n.data})}}removeCatch(){return this._def.innerType}}Et.create=(t,e)=>new Et({innerType:t,typeName:b.ZodCatch,catchValue:typeof e.catch=="function"?e.catch:()=>e.catch,...R(e)});class Sr extends M{_parse(e){if(this._getType(e)!==S.nan){const n=this._getOrReturnCtx(e);return k(n,{code:_.invalid_type,expected:S.nan,received:n.parsedType}),C}return{status:"valid",value:e.data}}}Sr.create=t=>new Sr({typeName:b.ZodNaN,...R(t)});class Fu extends M{_parse(e){const{ctx:r}=this._processInputParams(e),n=r.data;return this._def.type._parse({data:n,path:r.path,parent:r})}unwrap(){return this._def.type}}class Mt extends M{_parse(e){const{status:r,ctx:n}=this._processInputParams(e);if(n.common.async)return(async()=>{const s=await this._def.in._parseAsync({data:n.data,path:n.path,parent:n});return s.status==="aborted"?C:s.status==="dirty"?(r.dirty(),ze(s.value)):this._def.out._parseAsync({data:s.value,path:n.path,parent:n})})();{const a=this._def.in._parseSync({data:n.data,path:n.path,parent:n});return a.status==="aborted"?C:a.status==="dirty"?(r.dirty(),{status:"dirty",value:a.value}):this._def.out._parseSync({data:a.value,path:n.path,parent:n})}}static create(e,r){return new Mt({in:e,out:r,typeName:b.ZodPipeline})}}class Zt extends M{_parse(e){const r=this._def.innerType._parse(e),n=a=>(Se(a)&&(a.value=Object.freeze(a.value)),a);return et(r)?r.then(a=>n(a)):n(r)}unwrap(){return this._def.innerType}}Zt.create=(t,e)=>new Zt({innerType:t,typeName:b.ZodReadonly,...R(e)});var b;(function(t){t.ZodString="ZodString",t.ZodNumber="ZodNumber",t.ZodNaN="ZodNaN",t.ZodBigInt="ZodBigInt",t.ZodBoolean="ZodBoolean",t.ZodDate="ZodDate",t.ZodSymbol="ZodSymbol",t.ZodUndefined="ZodUndefined",t.ZodNull="ZodNull",t.ZodAny="ZodAny",t.ZodUnknown="ZodUnknown",t.ZodNever="ZodNever",t.ZodVoid="ZodVoid",t.ZodArray="ZodArray",t.ZodObject="ZodObject",t.ZodUnion="ZodUnion",t.ZodDiscriminatedUnion="ZodDiscriminatedUnion",t.ZodIntersection="ZodIntersection",t.ZodTuple="ZodTuple",t.ZodRecord="ZodRecord",t.ZodMap="ZodMap",t.ZodSet="ZodSet",t.ZodFunction="ZodFunction",t.ZodLazy="ZodLazy",t.ZodLiteral="ZodLiteral",t.ZodEnum="ZodEnum",t.ZodEffects="ZodEffects",t.ZodNativeEnum="ZodNativeEnum",t.ZodOptional="ZodOptional",t.ZodNullable="ZodNullable",t.ZodDefault="ZodDefault",t.ZodCatch="ZodCatch",t.ZodPromise="ZodPromise",t.ZodBranded="ZodBranded",t.ZodPipeline="ZodPipeline",t.ZodReadonly="ZodReadonly"})(b||(b={}));ge.create;oe.create;rt.create;nt.create;be.create;Te.create;at.create;ue.create;Ze.create;function Vu(){return{}}function Bu(t,e){var n,a,s;const r={type:"array"};return(n=t.type)!=null&&n._def&&((s=(a=t.type)==null?void 0:a._def)==null?void 0:s.typeName)!==b.ZodAny&&(r.items=D(t.type._def,{...e,currentPath:[...e.currentPath,"items"]})),t.minLength&&U(r,"minItems",t.minLength.value,t.minLength.message,e),t.maxLength&&U(r,"maxItems",t.maxLength.value,t.maxLength.message,e),t.exactLength&&(U(r,"minItems",t.exactLength.value,t.exactLength.message,e),U(r,"maxItems",t.exactLength.value,t.exactLength.message,e)),r}function Ju(t,e){const r={type:"integer",format:"int64"};if(!t.checks)return r;for(const n of t.checks)switch(n.kind){case"min":e.target==="jsonSchema7"?n.inclusive?U(r,"minimum",n.value,n.message,e):U(r,"exclusiveMinimum",n.value,n.message,e):(n.inclusive||(r.exclusiveMinimum=!0),U(r,"minimum",n.value,n.message,e));break;case"max":e.target==="jsonSchema7"?n.inclusive?U(r,"maximum",n.value,n.message,e):U(r,"exclusiveMaximum",n.value,n.message,e):(n.inclusive||(r.exclusiveMaximum=!0),U(r,"maximum",n.value,n.message,e));break;case"multipleOf":U(r,"multipleOf",n.value,n.message,e);break}return r}function Wu(){return{type:"boolean"}}function Sn(t,e){return D(t.type._def,e)}const qu=(t,e)=>D(t.innerType._def,e);function Tn(t,e,r){const n=r??e.dateStrategy;if(Array.isArray(n))return{anyOf:n.map((a,s)=>Tn(t,e,a))};switch(n){case"string":case"format:date-time":return{type:"string",format:"date-time"};case"format:date":return{type:"string",format:"date"};case"integer":return Yu(t,e)}}const Yu=(t,e)=>{const r={type:"integer",format:"unix-time"};if(e.target==="openApi3")return r;for(const n of t.checks)switch(n.kind){case"min":U(r,"minimum",n.value,n.message,e);break;case"max":U(r,"maximum",n.value,n.message,e);break}return r};function Ku(t,e){return{...D(t.innerType._def,e),default:t.defaultValue()}}function Gu(t,e){return e.effectStrategy==="input"?D(t.schema._def,e):{}}function Hu(t){return{type:"string",enum:Array.from(t.values)}}const Xu=t=>"type"in t&&t.type==="string"?!1:"allOf"in t;function Qu(t,e){const r=[D(t.left._def,{...e,currentPath:[...e.currentPath,"allOf","0"]}),D(t.right._def,{...e,currentPath:[...e.currentPath,"allOf","1"]})].filter(s=>!!s);let n=e.target==="jsonSchema2019-09"?{unevaluatedProperties:!1}:void 0;const a=[];return r.forEach(s=>{if(Xu(s))a.push(...s.allOf),s.unevaluatedProperties===void 0&&(n=void 0);else{let o=s;if("additionalProperties"in s&&s.additionalProperties===!1){const{additionalProperties:i,...c}=s;o=c}else n=void 0;a.push(o)}}),a.length?{allOf:a,...n}:void 0}function ec(t,e){const r=typeof t.value;return r!=="bigint"&&r!=="number"&&r!=="boolean"&&r!=="string"?{type:Array.isArray(t.value)?"array":"object"}:e.target==="openApi3"?{type:r==="bigint"?"integer":r,enum:[t.value]}:{type:r==="bigint"?"integer":r,const:t.value}}let yt;const se={cuid:/^[cC][^\s-]{8,}$/,cuid2:/^[0-9a-z]+$/,ulid:/^[0-9A-HJKMNP-TV-Z]{26}$/,email:/^(?!\.)(?!.*\.\.)([a-zA-Z0-9_'+\-\.]*)[a-zA-Z0-9_+-]@([a-zA-Z0-9][a-zA-Z0-9\-]*\.)+[a-zA-Z]{2,}$/,emoji:()=>(yt===void 0&&(yt=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),yt),uuid:/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/,ipv4:/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,ipv4Cidr:/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,ipv6:/^(([a-f0-9]{1,4}:){7}|::([a-f0-9]{1,4}:){0,6}|([a-f0-9]{1,4}:){1}:([a-f0-9]{1,4}:){0,5}|([a-f0-9]{1,4}:){2}:([a-f0-9]{1,4}:){0,4}|([a-f0-9]{1,4}:){3}:([a-f0-9]{1,4}:){0,3}|([a-f0-9]{1,4}:){4}:([a-f0-9]{1,4}:){0,2}|([a-f0-9]{1,4}:){5}:([a-f0-9]{1,4}:){0,1})([a-f0-9]{1,4}|(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2})))$/,ipv6Cidr:/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,base64:/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,base64url:/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,nanoid:/^[a-zA-Z0-9_-]{21}$/,jwt:/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/};function En(t,e){const r={type:"string"};if(t.checks)for(const n of t.checks)switch(n.kind){case"min":U(r,"minLength",typeof r.minLength=="number"?Math.max(r.minLength,n.value):n.value,n.message,e);break;case"max":U(r,"maxLength",typeof r.maxLength=="number"?Math.min(r.maxLength,n.value):n.value,n.message,e);break;case"email":switch(e.emailStrategy){case"format:email":ie(r,"email",n.message,e);break;case"format:idn-email":ie(r,"idn-email",n.message,e);break;case"pattern:zod":ee(r,se.email,n.message,e);break}break;case"url":ie(r,"uri",n.message,e);break;case"uuid":ie(r,"uuid",n.message,e);break;case"regex":ee(r,n.regex,n.message,e);break;case"cuid":ee(r,se.cuid,n.message,e);break;case"cuid2":ee(r,se.cuid2,n.message,e);break;case"startsWith":ee(r,RegExp(`^${_t(n.value,e)}`),n.message,e);break;case"endsWith":ee(r,RegExp(`${_t(n.value,e)}$`),n.message,e);break;case"datetime":ie(r,"date-time",n.message,e);break;case"date":ie(r,"date",n.message,e);break;case"time":ie(r,"time",n.message,e);break;case"duration":ie(r,"duration",n.message,e);break;case"length":U(r,"minLength",typeof r.minLength=="number"?Math.max(r.minLength,n.value):n.value,n.message,e),U(r,"maxLength",typeof r.maxLength=="number"?Math.min(r.maxLength,n.value):n.value,n.message,e);break;case"includes":{ee(r,RegExp(_t(n.value,e)),n.message,e);break}case"ip":{n.version!=="v6"&&ie(r,"ipv4",n.message,e),n.version!=="v4"&&ie(r,"ipv6",n.message,e);break}case"base64url":ee(r,se.base64url,n.message,e);break;case"jwt":ee(r,se.jwt,n.message,e);break;case"cidr":{n.version!=="v6"&&ee(r,se.ipv4Cidr,n.message,e),n.version!=="v4"&&ee(r,se.ipv6Cidr,n.message,e);break}case"emoji":ee(r,se.emoji(),n.message,e);break;case"ulid":{ee(r,se.ulid,n.message,e);break}case"base64":{switch(e.base64Strategy){case"format:binary":{ie(r,"binary",n.message,e);break}case"contentEncoding:base64":{U(r,"contentEncoding","base64",n.message,e);break}case"pattern:zod":{ee(r,se.base64,n.message,e);break}}break}case"nanoid":ee(r,se.nanoid,n.message,e)}return r}function _t(t,e){return e.patternStrategy==="escape"?rc(t):t}const tc=new Set("ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvxyz0123456789");function rc(t){let e="";for(let r=0;r<t.length;r++)tc.has(t[r])||(e+="\\"),e+=t[r];return e}function ie(t,e,r,n){var a;t.format||(a=t.anyOf)!=null&&a.some(s=>s.format)?(t.anyOf||(t.anyOf=[]),t.format&&(t.anyOf.push({format:t.format,...t.errorMessage&&n.errorMessages&&{errorMessage:{format:t.errorMessage.format}}}),delete t.format,t.errorMessage&&(delete t.errorMessage.format,Object.keys(t.errorMessage).length===0&&delete t.errorMessage)),t.anyOf.push({format:e,...r&&n.errorMessages&&{errorMessage:{format:r}}})):U(t,"format",e,r,n)}function ee(t,e,r,n){var a;t.pattern||(a=t.allOf)!=null&&a.some(s=>s.pattern)?(t.allOf||(t.allOf=[]),t.pattern&&(t.allOf.push({pattern:t.pattern,...t.errorMessage&&n.errorMessages&&{errorMessage:{pattern:t.errorMessage.pattern}}}),delete t.pattern,t.errorMessage&&(delete t.errorMessage.pattern,Object.keys(t.errorMessage).length===0&&delete t.errorMessage)),t.allOf.push({pattern:Tr(e,n),...r&&n.errorMessages&&{errorMessage:{pattern:r}}})):U(t,"pattern",Tr(e,n),r,n)}function Tr(t,e){var c;if(!e.applyRegexFlags||!t.flags)return t.source;const r={i:t.flags.includes("i"),m:t.flags.includes("m"),s:t.flags.includes("s")},n=r.i?t.source.toLowerCase():t.source;let a="",s=!1,o=!1,i=!1;for(let u=0;u<n.length;u++){if(s){a+=n[u],s=!1;continue}if(r.i){if(o){if(n[u].match(/[a-z]/)){i?(a+=n[u],a+=`${n[u-2]}-${n[u]}`.toUpperCase(),i=!1):n[u+1]==="-"&&((c=n[u+2])!=null&&c.match(/[a-z]/))?(a+=n[u],i=!0):a+=`${n[u]}${n[u].toUpperCase()}`;continue}}else if(n[u].match(/[a-z]/)){a+=`[${n[u]}${n[u].toUpperCase()}]`;continue}}if(r.m){if(n[u]==="^"){a+=`(^|(?<=[\r
]))`;continue}else if(n[u]==="$"){a+=`($|(?=[\r
]))`;continue}}if(r.s&&n[u]==="."){a+=o?`${n[u]}\r
`:`[${n[u]}\r
]`;continue}a+=n[u],n[u]==="\\"?s=!0:o&&n[u]==="]"?o=!1:!o&&n[u]==="["&&(o=!0)}try{new RegExp(a)}catch{return console.warn(`Could not convert regex pattern at ${e.currentPath.join("/")} to a flag-independent form! Falling back to the flag-ignorant source`),t.source}return a}function Zn(t,e){var n,a,s,o,i,c;if(e.target==="openAi"&&console.warn("Warning: OpenAI may not support records in schemas! Try an array of key-value pairs instead."),e.target==="openApi3"&&((n=t.keyType)==null?void 0:n._def.typeName)===b.ZodEnum)return{type:"object",required:t.keyType._def.values,properties:t.keyType._def.values.reduce((u,d)=>({...u,[d]:D(t.valueType._def,{...e,currentPath:[...e.currentPath,"properties",d]})??{}}),{}),additionalProperties:!1};const r={type:"object",additionalProperties:D(t.valueType._def,{...e,currentPath:[...e.currentPath,"additionalProperties"]})??{}};if(e.target==="openApi3")return r;if(((a=t.keyType)==null?void 0:a._def.typeName)===b.ZodString&&((s=t.keyType._def.checks)!=null&&s.length)){const{type:u,...d}=En(t.keyType._def,e);return{...r,propertyNames:d}}else{if(((o=t.keyType)==null?void 0:o._def.typeName)===b.ZodEnum)return{...r,propertyNames:{enum:t.keyType._def.values}};if(((i=t.keyType)==null?void 0:i._def.typeName)===b.ZodBranded&&t.keyType._def.type._def.typeName===b.ZodString&&((c=t.keyType._def.type._def.checks)!=null&&c.length)){const{type:u,...d}=Sn(t.keyType._def,e);return{...r,propertyNames:d}}}return r}function nc(t,e){if(e.mapStrategy==="record")return Zn(t,e);const r=D(t.keyType._def,{...e,currentPath:[...e.currentPath,"items","items","0"]})||{},n=D(t.valueType._def,{...e,currentPath:[...e.currentPath,"items","items","1"]})||{};return{type:"array",maxItems:125,items:{type:"array",items:[r,n],minItems:2,maxItems:2}}}function ac(t){const e=t.values,n=Object.keys(t.values).filter(s=>typeof e[e[s]]!="number").map(s=>e[s]),a=Array.from(new Set(n.map(s=>typeof s)));return{type:a.length===1?a[0]==="string"?"string":"number":["string","number"],enum:n}}function sc(){return{not:{}}}function ic(t){return t.target==="openApi3"?{enum:["null"],nullable:!0}:{type:"null"}}const st={ZodString:"string",ZodNumber:"number",ZodBigInt:"integer",ZodBoolean:"boolean",ZodNull:"null"};function oc(t,e){if(e.target==="openApi3")return Er(t,e);const r=t.options instanceof Map?Array.from(t.options.values()):t.options;if(r.every(n=>n._def.typeName in st&&(!n._def.checks||!n._def.checks.length))){const n=r.reduce((a,s)=>{const o=st[s._def.typeName];return o&&!a.includes(o)?[...a,o]:a},[]);return{type:n.length>1?n:n[0]}}else if(r.every(n=>n._def.typeName==="ZodLiteral"&&!n.description)){const n=r.reduce((a,s)=>{const o=typeof s._def.value;switch(o){case"string":case"number":case"boolean":return[...a,o];case"bigint":return[...a,"integer"];case"object":if(s._def.value===null)return[...a,"null"];case"symbol":case"undefined":case"function":default:return a}},[]);if(n.length===r.length){const a=n.filter((s,o,i)=>i.indexOf(s)===o);return{type:a.length>1?a:a[0],enum:r.reduce((s,o)=>s.includes(o._def.value)?s:[...s,o._def.value],[])}}}else if(r.every(n=>n._def.typeName==="ZodEnum"))return{type:"string",enum:r.reduce((n,a)=>[...n,...a._def.values.filter(s=>!n.includes(s))],[])};return Er(t,e)}const Er=(t,e)=>{const r=(t.options instanceof Map?Array.from(t.options.values()):t.options).map((n,a)=>D(n._def,{...e,currentPath:[...e.currentPath,"anyOf",`${a}`]})).filter(n=>!!n&&(!e.strictUnions||typeof n=="object"&&Object.keys(n).length>0));return r.length?{anyOf:r}:void 0};function uc(t,e){if(["ZodString","ZodNumber","ZodBigInt","ZodBoolean","ZodNull"].includes(t.innerType._def.typeName)&&(!t.innerType._def.checks||!t.innerType._def.checks.length))return e.target==="openApi3"?{type:st[t.innerType._def.typeName],nullable:!0}:{type:[st[t.innerType._def.typeName],"null"]};if(e.target==="openApi3"){const n=D(t.innerType._def,{...e,currentPath:[...e.currentPath]});return n&&"$ref"in n?{allOf:[n],nullable:!0}:n&&{...n,nullable:!0}}const r=D(t.innerType._def,{...e,currentPath:[...e.currentPath,"anyOf","0"]});return r&&{anyOf:[r,{type:"null"}]}}function cc(t,e){const r={type:"number"};if(!t.checks)return r;for(const n of t.checks)switch(n.kind){case"int":r.type="integer",wn(r,"type",n.message,e);break;case"min":e.target==="jsonSchema7"?n.inclusive?U(r,"minimum",n.value,n.message,e):U(r,"exclusiveMinimum",n.value,n.message,e):(n.inclusive||(r.exclusiveMinimum=!0),U(r,"minimum",n.value,n.message,e));break;case"max":e.target==="jsonSchema7"?n.inclusive?U(r,"maximum",n.value,n.message,e):U(r,"exclusiveMaximum",n.value,n.message,e):(n.inclusive||(r.exclusiveMaximum=!0),U(r,"maximum",n.value,n.message,e));break;case"multipleOf":U(r,"multipleOf",n.value,n.message,e);break}return r}function lc(t,e){return e.removeAdditionalStrategy==="strict"?t.catchall._def.typeName==="ZodNever"?t.unknownKeys!=="strict":D(t.catchall._def,{...e,currentPath:[...e.currentPath,"additionalProperties"]})??!0:t.catchall._def.typeName==="ZodNever"?t.unknownKeys==="passthrough":D(t.catchall._def,{...e,currentPath:[...e.currentPath,"additionalProperties"]})??!0}function dc(t,e){const r=e.target==="openAi",n={type:"object",...Object.entries(t.shape()).reduce((a,[s,o])=>{if(o===void 0||o._def===void 0)return a;let i=o.isOptional();i&&r&&(o instanceof ue&&(o=o._def.innerType),o.isNullable()||(o=o.nullable()),i=!1);const c=D(o._def,{...e,currentPath:[...e.currentPath,"properties",s],propertyPath:[...e.currentPath,"properties",s]});return c===void 0?a:{properties:{...a.properties,[s]:c},required:i?a.required:[...a.required,s]}},{properties:{},required:[]}),additionalProperties:lc(t,e)};return n.required.length||delete n.required,n}const pc=(t,e)=>{var n;if(e.currentPath.toString()===((n=e.propertyPath)==null?void 0:n.toString()))return D(t.innerType._def,e);const r=D(t.innerType._def,{...e,currentPath:[...e.currentPath,"anyOf","1"]});return r?{anyOf:[{not:{}},r]}:{}},fc=(t,e)=>{if(e.pipeStrategy==="input")return D(t.in._def,e);if(e.pipeStrategy==="output")return D(t.out._def,e);const r=D(t.in._def,{...e,currentPath:[...e.currentPath,"allOf","0"]}),n=D(t.out._def,{...e,currentPath:[...e.currentPath,"allOf",r?"1":"0"]});return{allOf:[r,n].filter(a=>a!==void 0)}};function hc(t,e){return D(t.type._def,e)}function mc(t,e){const n={type:"array",uniqueItems:!0,items:D(t.valueType._def,{...e,currentPath:[...e.currentPath,"items"]})};return t.minSize&&U(n,"minItems",t.minSize.value,t.minSize.message,e),t.maxSize&&U(n,"maxItems",t.maxSize.value,t.maxSize.message,e),n}function gc(t,e){return t.rest?{type:"array",minItems:t.items.length,items:t.items.map((r,n)=>D(r._def,{...e,currentPath:[...e.currentPath,"items",`${n}`]})).reduce((r,n)=>n===void 0?r:[...r,n],[]),additionalItems:D(t.rest._def,{...e,currentPath:[...e.currentPath,"additionalItems"]})}:{type:"array",minItems:t.items.length,maxItems:t.items.length,items:t.items.map((r,n)=>D(r._def,{...e,currentPath:[...e.currentPath,"items",`${n}`]})).reduce((r,n)=>n===void 0?r:[...r,n],[])}}function vc(){return{not:{}}}function yc(){return{}}const _c=(t,e)=>D(t.innerType._def,e);function D(t,e,r=!1){var o;const n=e.seen.get(t);if(e.override){const i=(o=e.override)==null?void 0:o.call(e,t,e,n,r);if(i!==hu)return i}if(n&&!r){const i=bc(n,e);if(i!==void 0)return i}const a={def:t,path:e.currentPath,jsonSchema:void 0};e.seen.set(t,a);const s=kc(t,t.typeName,e);return s&&xc(t,e,s),a.jsonSchema=s,s}const bc=(t,e)=>{switch(e.$refStrategy){case"root":return{$ref:t.path.join("/")};case"relative":return{$ref:wc(e.currentPath,t.path)};case"none":case"seen":return t.path.length<e.currentPath.length&&t.path.every((r,n)=>e.currentPath[n]===r)?(console.warn(`Recursive reference detected at ${e.currentPath.join("/")}! Defaulting to any`),{}):e.$refStrategy==="seen"?{}:void 0}},wc=(t,e)=>{let r=0;for(;r<t.length&&r<e.length&&t[r]===e[r];r++);return[(t.length-r).toString(),...e.slice(r)].join("/")},kc=(t,e,r)=>{switch(e){case b.ZodString:return En(t,r);case b.ZodNumber:return cc(t,r);case b.ZodObject:return dc(t,r);case b.ZodBigInt:return Ju(t,r);case b.ZodBoolean:return Wu();case b.ZodDate:return Tn(t,r);case b.ZodUndefined:return vc();case b.ZodNull:return ic(r);case b.ZodArray:return Bu(t,r);case b.ZodUnion:case b.ZodDiscriminatedUnion:return oc(t,r);case b.ZodIntersection:return Qu(t,r);case b.ZodTuple:return gc(t,r);case b.ZodRecord:return Zn(t,r);case b.ZodLiteral:return ec(t,r);case b.ZodEnum:return Hu(t);case b.ZodNativeEnum:return ac(t);case b.ZodNullable:return uc(t,r);case b.ZodOptional:return pc(t,r);case b.ZodMap:return nc(t,r);case b.ZodSet:return mc(t,r);case b.ZodLazy:return D(t.getter()._def,r);case b.ZodPromise:return hc(t,r);case b.ZodNaN:case b.ZodNever:return sc();case b.ZodEffects:return Gu(t,r);case b.ZodAny:return Vu();case b.ZodUnknown:return yc();case b.ZodDefault:return Ku(t,r);case b.ZodBranded:return Sn(t,r);case b.ZodReadonly:return _c(t,r);case b.ZodCatch:return qu(t,r);case b.ZodPipeline:return fc(t,r);case b.ZodFunction:case b.ZodVoid:case b.ZodSymbol:return;default:return(n=>{})()}},xc=(t,e,r)=>(t.description&&(r.description=t.description,e.markdownDescription&&(r.markdownDescription=t.description)),r),Ic=(t,e)=>{const r=gu(e),n=typeof e=="object"&&e.definitions?Object.entries(e.definitions).reduce((c,[u,d])=>({...c,[u]:D(d._def,{...r,currentPath:[...r.basePath,r.definitionPath,u]},!0)??{}}),{}):void 0,a=typeof e=="string"?e:(e==null?void 0:e.nameStrategy)==="title"||e==null?void 0:e.name,s=D(t._def,a===void 0?r:{...r,currentPath:[...r.basePath,r.definitionPath,a]},!1)??{},o=typeof e=="object"&&e.name!==void 0&&e.nameStrategy==="title"?e.name:void 0;o!==void 0&&(s.title=o);const i=a===void 0?n?{...s,[r.definitionPath]:n}:s:{$ref:[...r.$refStrategy==="relative"?[]:r.basePath,r.definitionPath,a].join("/"),[r.definitionPath]:{...n,[a]:s}};return r.target==="jsonSchema7"?i.$schema="http://json-schema.org/draft-07/schema#":(r.target==="jsonSchema2019-09"||r.target==="openAi")&&(i.$schema="https://json-schema.org/draft/2019-09/schema#"),r.target==="openAi"&&("anyOf"in i||"oneOf"in i||"allOf"in i||"type"in i&&Array.isArray(i.type))&&console.warn("Warning: OpenAI may not support schemas with unions as roots! Try wrapping it in an object property."),i};var Je=({prefix:t,size:e=16,alphabet:r="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",separator:n="-"}={})=>{const a=()=>{const s=r.length,o=new Array(e);for(let i=0;i<e;i++)o[i]=r[Math.random()*s|0];return o.join("")};if(t==null)return a;if(r.includes(n))throw new oa({argument:"separator",message:`The separator "${n}" must not be part of the alphabet "${r}".`});return()=>`${t}${n}${a()}`},Sc=Je();function Ll(t){return(t instanceof Error||t instanceof DOMException)&&(t.name==="AbortError"||t.name==="ResponseAborted"||t.name==="TimeoutError")}var Tc=/"__proto__"\s*:/,Ec=/"constructor"\s*:/;function Zc(t){const e=JSON.parse(t);return e===null||typeof e!="object"||Tc.test(t)===!1&&Ec.test(t)===!1?e:$c(e)}function $c(t){let e=[t];for(;e.length;){const r=e;e=[];for(const n of r){if(Object.prototype.hasOwnProperty.call(n,"__proto__"))throw new SyntaxError("Object contains forbidden prototype property");if(Object.prototype.hasOwnProperty.call(n,"constructor")&&Object.prototype.hasOwnProperty.call(n.constructor,"prototype"))throw new SyntaxError("Object contains forbidden prototype property");for(const a in n){const s=n[a];s&&typeof s=="object"&&e.push(s)}}}return t}function Oc(t){const{stackTraceLimit:e}=Error;Error.stackTraceLimit=0;try{return Zc(t)}finally{Error.stackTraceLimit=e}}var it=Symbol.for("vercel.ai.validator");function zc(t){return{[it]:!0,validate:t}}function Ac(t){return typeof t=="object"&&t!==null&&it in t&&t[it]===!0&&"validate"in t}function Nc(t){return Ac(t)?t:Cc(t)}function Cc(t){return zc(async e=>{const r=await t["~standard"].validate(e);return r.issues==null?{success:!0,value:r.value}:{success:!1,error:new Ke({value:e,cause:r.issues})}})}async function Zr({value:t,schema:e}){const r=await jt({value:t,schema:e});if(!r.success)throw Ke.wrap({value:t,cause:r.error});return r.value}async function jt({value:t,schema:e}){const r=Nc(e);try{if(r.validate==null)return{success:!0,value:t,rawValue:t};const n=await r.validate(t);return n.success?{success:!0,value:n.value,rawValue:t}:{success:!1,error:Ke.wrap({value:t,cause:n.error}),rawValue:t}}catch(n){return{success:!1,error:Ke.wrap({value:t,cause:n}),rawValue:t}}}async function ot({text:t,schema:e}){try{const r=Oc(t);return e==null?{success:!0,value:r,rawValue:r}:await jt({value:r,schema:e})}catch(r){return{success:!1,error:Ft.isInstance(r)?r:new Ft({text:t,cause:r}),rawValue:void 0}}}function $n({stream:t,schema:e}){return t.pipeThrough(new TextDecoderStream).pipeThrough(new fa).pipeThrough(new TransformStream({async transform({data:r},n){r!=="[DONE]"&&n.enqueue(await ot({text:r,schema:e}))}}))}async function ke(t){return typeof t=="function"&&(t=t()),Promise.resolve(t)}function Rc(t,e){var r;const n=(r=void 0)!=null?r:!1;return Dt(Ic(t,{$refStrategy:n?"root":"none",target:"jsonSchema7"}),{validate:async a=>{const s=await t.safeParseAsync(a);return s.success?{success:!0,value:s.data}:{success:!1,error:s.error}}})}function Pc(t,e){var r;const n=(r=void 0)!=null?r:!1,a=so(t,{target:"draft-7",io:"output",reused:n?"ref":"inline"});return Dt(a,{validate:async s=>{const o=await fn(t,s);return o.success?{success:!0,value:o.data}:{success:!1,error:o.error}}})}function Mc(t){return"_zod"in t}function jc(t,e){return Mc(t)?Pc(t):Rc(t)}var $t=Symbol.for("vercel.ai.schema");function Dt(t,{validate:e}={}){return{[$t]:!0,_type:void 0,[it]:!0,jsonSchema:t,validate:e}}function Dc(t){return typeof t=="object"&&t!==null&&$t in t&&t[$t]===!0&&"jsonSchema"in t&&"validate"in t}function Lc(t){return t==null?Dt({properties:{},additionalProperties:!1}):Dc(t)?t:jc(t)}var Uc=Object.defineProperty,Fc=(t,e)=>{for(var r in e)Uc(t,r,{get:e[r],enumerable:!0})},On="AI_NoObjectGeneratedError",zn=`vercel.ai.error.${On}`,Vc=Symbol.for(zn),An,$r=class extends pe{constructor({message:t="No object generated.",cause:e,text:r,response:n,usage:a,finishReason:s}){super({name:On,message:t,cause:e}),this[An]=!0,this.text=r,this.response=n,this.usage=a,this.finishReason=s}static isInstance(t){return pe.hasMarker(t,zn)}};An=Vc;var Nn=Q([m(),Qe(Uint8Array),Qe(ArrayBuffer),du(t=>{var e,r;return(r=(e=globalThis.Buffer)==null?void 0:e.isBuffer(t))!=null?r:!1},{message:"Must be a Buffer"})]),Le=cu(()=>Q([Do(),m(),Pe(),K(),kt(m(),Le),le(Le)])),P=kt(m(),kt(m(),Le)),Cn=N({type:I("text"),text:m(),providerOptions:P.optional()}),Bc=N({type:I("image"),image:Q([Nn,Qe(URL)]),mediaType:m().optional(),providerOptions:P.optional()}),Rn=N({type:I("file"),data:Q([Nn,Qe(URL)]),filename:m().optional(),mediaType:m(),providerOptions:P.optional()}),Jc=N({type:I("reasoning"),text:m(),providerOptions:P.optional()}),Wc=N({type:I("tool-call"),toolCallId:m(),toolName:m(),input:B(),providerOptions:P.optional(),providerExecuted:K().optional()}),qc=Bo("type",[N({type:I("text"),value:m()}),N({type:I("json"),value:Le}),N({type:I("error-text"),value:m()}),N({type:I("error-json"),value:Le}),N({type:I("content"),value:le(Q([N({type:I("text"),text:m()}),N({type:I("media"),data:m(),mediaType:m()})]))})]),Pn=N({type:I("tool-result"),toolCallId:m(),toolName:m(),output:qc,providerOptions:P.optional()}),Yc=N({role:I("system"),content:m(),providerOptions:P.optional()}),Kc=N({role:I("user"),content:Q([m(),le(Q([Cn,Bc,Rn]))]),providerOptions:P.optional()}),Gc=N({role:I("assistant"),content:Q([m(),le(Q([Cn,Rn,Jc,Wc,Pn]))]),providerOptions:P.optional()}),Hc=N({role:I("tool"),content:le(Pn),providerOptions:P.optional()});Q([Yc,Kc,Gc,Hc]);Je({prefix:"aitxt",size:24});(class extends TransformStream{constructor(){super({transform(t,e){e.enqueue(`data: ${JSON.stringify(t)}

`)},flush(t){t.enqueue(`data: [DONE]

`)}})}});var Mn=Q([V({type:I("text-start"),id:m(),providerMetadata:P.optional()}),V({type:I("text-delta"),id:m(),delta:m(),providerMetadata:P.optional()}),V({type:I("text-end"),id:m(),providerMetadata:P.optional()}),V({type:I("error"),errorText:m()}),V({type:I("tool-input-start"),toolCallId:m(),toolName:m(),providerExecuted:K().optional(),dynamic:K().optional()}),V({type:I("tool-input-delta"),toolCallId:m(),inputTextDelta:m()}),V({type:I("tool-input-available"),toolCallId:m(),toolName:m(),input:B(),providerExecuted:K().optional(),providerMetadata:P.optional(),dynamic:K().optional()}),V({type:I("tool-input-error"),toolCallId:m(),toolName:m(),input:B(),providerExecuted:K().optional(),providerMetadata:P.optional(),dynamic:K().optional(),errorText:m()}),V({type:I("tool-output-available"),toolCallId:m(),output:B(),providerExecuted:K().optional(),dynamic:K().optional(),preliminary:K().optional()}),V({type:I("tool-output-error"),toolCallId:m(),errorText:m(),providerExecuted:K().optional(),dynamic:K().optional()}),V({type:I("reasoning"),text:m(),providerMetadata:P.optional()}),V({type:I("reasoning-start"),id:m(),providerMetadata:P.optional()}),V({type:I("reasoning-delta"),id:m(),delta:m(),providerMetadata:P.optional()}),V({type:I("reasoning-end"),id:m(),providerMetadata:P.optional()}),V({type:I("reasoning-part-finish")}),V({type:I("source-url"),sourceId:m(),url:m(),title:m().optional(),providerMetadata:P.optional()}),V({type:I("source-document"),sourceId:m(),mediaType:m(),title:m(),filename:m().optional(),providerMetadata:P.optional()}),V({type:I("file"),url:m(),mediaType:m(),providerMetadata:P.optional()}),V({type:m().startsWith("data-"),id:m().optional(),data:B(),transient:K().optional()}),V({type:I("start-step")}),V({type:I("finish-step")}),V({type:I("start"),messageId:m().optional(),messageMetadata:B().optional()}),V({type:I("finish"),messageMetadata:B().optional()}),V({type:I("abort")}),V({type:I("message-metadata"),messageMetadata:B()})]);function Xc(t){return t.type.startsWith("data-")}function jn(t,e){if(t===void 0&&e===void 0)return;if(t===void 0)return e;if(e===void 0)return t;const r={...t};for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)){const a=e[n];if(a===void 0)continue;const s=n in t?t[n]:void 0,o=a!==null&&typeof a=="object"&&!Array.isArray(a)&&!(a instanceof Date)&&!(a instanceof RegExp),i=s!=null&&typeof s=="object"&&!Array.isArray(s)&&!(s instanceof Date)&&!(s instanceof RegExp);o&&i?r[n]=jn(s,a):r[n]=a}return r}function Qc(t){const e=["ROOT"];let r=-1,n=null;function a(c,u,d){switch(c){case'"':{r=u,e.pop(),e.push(d),e.push("INSIDE_STRING");break}case"f":case"t":case"n":{r=u,n=u,e.pop(),e.push(d),e.push("INSIDE_LITERAL");break}case"-":{e.pop(),e.push(d),e.push("INSIDE_NUMBER");break}case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":{r=u,e.pop(),e.push(d),e.push("INSIDE_NUMBER");break}case"{":{r=u,e.pop(),e.push(d),e.push("INSIDE_OBJECT_START");break}case"[":{r=u,e.pop(),e.push(d),e.push("INSIDE_ARRAY_START");break}}}function s(c,u){switch(c){case",":{e.pop(),e.push("INSIDE_OBJECT_AFTER_COMMA");break}case"}":{r=u,e.pop();break}}}function o(c,u){switch(c){case",":{e.pop(),e.push("INSIDE_ARRAY_AFTER_COMMA");break}case"]":{r=u,e.pop();break}}}for(let c=0;c<t.length;c++){const u=t[c];switch(e[e.length-1]){case"ROOT":a(u,c,"FINISH");break;case"INSIDE_OBJECT_START":{switch(u){case'"':{e.pop(),e.push("INSIDE_OBJECT_KEY");break}case"}":{r=c,e.pop();break}}break}case"INSIDE_OBJECT_AFTER_COMMA":{switch(u){case'"':{e.pop(),e.push("INSIDE_OBJECT_KEY");break}}break}case"INSIDE_OBJECT_KEY":{switch(u){case'"':{e.pop(),e.push("INSIDE_OBJECT_AFTER_KEY");break}}break}case"INSIDE_OBJECT_AFTER_KEY":{switch(u){case":":{e.pop(),e.push("INSIDE_OBJECT_BEFORE_VALUE");break}}break}case"INSIDE_OBJECT_BEFORE_VALUE":{a(u,c,"INSIDE_OBJECT_AFTER_VALUE");break}case"INSIDE_OBJECT_AFTER_VALUE":{s(u,c);break}case"INSIDE_STRING":{switch(u){case'"':{e.pop(),r=c;break}case"\\":{e.push("INSIDE_STRING_ESCAPE");break}default:r=c}break}case"INSIDE_ARRAY_START":{switch(u){case"]":{r=c,e.pop();break}default:{r=c,a(u,c,"INSIDE_ARRAY_AFTER_VALUE");break}}break}case"INSIDE_ARRAY_AFTER_VALUE":{switch(u){case",":{e.pop(),e.push("INSIDE_ARRAY_AFTER_COMMA");break}case"]":{r=c,e.pop();break}default:{r=c;break}}break}case"INSIDE_ARRAY_AFTER_COMMA":{a(u,c,"INSIDE_ARRAY_AFTER_VALUE");break}case"INSIDE_STRING_ESCAPE":{e.pop(),r=c;break}case"INSIDE_NUMBER":{switch(u){case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":{r=c;break}case"e":case"E":case"-":case".":break;case",":{e.pop(),e[e.length-1]==="INSIDE_ARRAY_AFTER_VALUE"&&o(u,c),e[e.length-1]==="INSIDE_OBJECT_AFTER_VALUE"&&s(u,c);break}case"}":{e.pop(),e[e.length-1]==="INSIDE_OBJECT_AFTER_VALUE"&&s(u,c);break}case"]":{e.pop(),e[e.length-1]==="INSIDE_ARRAY_AFTER_VALUE"&&o(u,c);break}default:{e.pop();break}}break}case"INSIDE_LITERAL":{const y=t.substring(n,c+1);!"false".startsWith(y)&&!"true".startsWith(y)&&!"null".startsWith(y)?(e.pop(),e[e.length-1]==="INSIDE_OBJECT_AFTER_VALUE"?s(u,c):e[e.length-1]==="INSIDE_ARRAY_AFTER_VALUE"&&o(u,c)):r=c;break}}}let i=t.slice(0,r+1);for(let c=e.length-1;c>=0;c--)switch(e[c]){case"INSIDE_STRING":{i+='"';break}case"INSIDE_OBJECT_KEY":case"INSIDE_OBJECT_AFTER_KEY":case"INSIDE_OBJECT_AFTER_COMMA":case"INSIDE_OBJECT_START":case"INSIDE_OBJECT_BEFORE_VALUE":case"INSIDE_OBJECT_AFTER_VALUE":{i+="}";break}case"INSIDE_ARRAY_START":case"INSIDE_ARRAY_AFTER_COMMA":case"INSIDE_ARRAY_AFTER_VALUE":{i+="]";break}case"INSIDE_LITERAL":{const d=t.substring(n,t.length);"true".startsWith(d)?i+="true".slice(d.length):"false".startsWith(d)?i+="false".slice(d.length):"null".startsWith(d)&&(i+="null".slice(d.length))}}return i}async function Dn(t){if(t===void 0)return{value:void 0,state:"undefined-input"};let e=await ot({text:t});return e.success?{value:e.value,state:"successful-parse"}:(e=await ot({text:Qc(t)}),e.success?{value:e.value,state:"repaired-parse"}:{value:void 0,state:"failed-parse"})}function Ne(t){return t.type.startsWith("tool-")}function Or(t){return t.type.split("-").slice(1).join("-")}function el({lastMessage:t,messageId:e}){return{message:(t==null?void 0:t.role)==="assistant"?t:{id:e,metadata:void 0,role:"assistant",parts:[]},activeTextParts:{},activeReasoningParts:{},partialToolCalls:{}}}function tl({stream:t,messageMetadataSchema:e,dataPartSchemas:r,runUpdateMessageJob:n,onError:a,onToolCall:s,onData:o}){return t.pipeThrough(new TransformStream({async transform(i,c){await n(async({state:u,write:d})=>{var y,x,O,w;function Z(p){const A=u.message.parts.filter(Ne).find(z=>z.toolCallId===p);if(A==null)throw new Error("tool-output-error must be preceded by a tool-input-available");return A}function g(p){const A=u.message.parts.filter(z=>z.type==="dynamic-tool").find(z=>z.toolCallId===p);if(A==null)throw new Error("tool-output-error must be preceded by a tool-input-available");return A}function l(p){var $;const A=u.message.parts.find(ve=>Ne(ve)&&ve.toolCallId===p.toolCallId),z=p,q=A;A!=null?(A.state=p.state,q.input=z.input,q.output=z.output,q.errorText=z.errorText,q.rawInput=z.rawInput,q.preliminary=z.preliminary,q.providerExecuted=($=z.providerExecuted)!=null?$:A.providerExecuted,z.providerMetadata!=null&&A.state==="input-available"&&(A.callProviderMetadata=z.providerMetadata)):u.message.parts.push({type:`tool-${p.toolName}`,toolCallId:p.toolCallId,state:p.state,input:z.input,output:z.output,rawInput:z.rawInput,errorText:z.errorText,providerExecuted:z.providerExecuted,preliminary:z.preliminary,...z.providerMetadata!=null?{callProviderMetadata:z.providerMetadata}:{}})}function h(p){var $;const A=u.message.parts.find(ve=>ve.type==="dynamic-tool"&&ve.toolCallId===p.toolCallId),z=p,q=A;A!=null?(A.state=p.state,q.toolName=p.toolName,q.input=z.input,q.output=z.output,q.errorText=z.errorText,q.rawInput=($=z.rawInput)!=null?$:q.rawInput,q.preliminary=z.preliminary,z.providerMetadata!=null&&A.state==="input-available"&&(A.callProviderMetadata=z.providerMetadata)):u.message.parts.push({type:"dynamic-tool",toolName:p.toolName,toolCallId:p.toolCallId,state:p.state,input:z.input,output:z.output,errorText:z.errorText,preliminary:z.preliminary,...z.providerMetadata!=null?{callProviderMetadata:z.providerMetadata}:{}})}async function v(p){if(p!=null){const $=u.message.metadata!=null?jn(u.message.metadata,p):p;e!=null&&await Zr({value:$,schema:e}),u.message.metadata=$}}switch(i.type){case"text-start":{const p={type:"text",text:"",providerMetadata:i.providerMetadata,state:"streaming"};u.activeTextParts[i.id]=p,u.message.parts.push(p),d();break}case"text-delta":{const p=u.activeTextParts[i.id];p.text+=i.delta,p.providerMetadata=(y=i.providerMetadata)!=null?y:p.providerMetadata,d();break}case"text-end":{const p=u.activeTextParts[i.id];p.state="done",p.providerMetadata=(x=i.providerMetadata)!=null?x:p.providerMetadata,delete u.activeTextParts[i.id],d();break}case"reasoning-start":{const p={type:"reasoning",text:"",providerMetadata:i.providerMetadata,state:"streaming"};u.activeReasoningParts[i.id]=p,u.message.parts.push(p),d();break}case"reasoning-delta":{const p=u.activeReasoningParts[i.id];p.text+=i.delta,p.providerMetadata=(O=i.providerMetadata)!=null?O:p.providerMetadata,d();break}case"reasoning-end":{const p=u.activeReasoningParts[i.id];p.providerMetadata=(w=i.providerMetadata)!=null?w:p.providerMetadata,p.state="done",delete u.activeReasoningParts[i.id],d();break}case"file":{u.message.parts.push({type:"file",mediaType:i.mediaType,url:i.url}),d();break}case"source-url":{u.message.parts.push({type:"source-url",sourceId:i.sourceId,url:i.url,title:i.title,providerMetadata:i.providerMetadata}),d();break}case"source-document":{u.message.parts.push({type:"source-document",sourceId:i.sourceId,mediaType:i.mediaType,title:i.title,filename:i.filename,providerMetadata:i.providerMetadata}),d();break}case"tool-input-start":{const p=u.message.parts.filter(Ne);u.partialToolCalls[i.toolCallId]={text:"",toolName:i.toolName,index:p.length,dynamic:i.dynamic},i.dynamic?h({toolCallId:i.toolCallId,toolName:i.toolName,state:"input-streaming",input:void 0}):l({toolCallId:i.toolCallId,toolName:i.toolName,state:"input-streaming",input:void 0,providerExecuted:i.providerExecuted}),d();break}case"tool-input-delta":{const p=u.partialToolCalls[i.toolCallId];p.text+=i.inputTextDelta;const{value:$}=await Dn(p.text);p.dynamic?h({toolCallId:i.toolCallId,toolName:p.toolName,state:"input-streaming",input:$}):l({toolCallId:i.toolCallId,toolName:p.toolName,state:"input-streaming",input:$}),d();break}case"tool-input-available":{i.dynamic?h({toolCallId:i.toolCallId,toolName:i.toolName,state:"input-available",input:i.input,providerMetadata:i.providerMetadata}):l({toolCallId:i.toolCallId,toolName:i.toolName,state:"input-available",input:i.input,providerExecuted:i.providerExecuted,providerMetadata:i.providerMetadata}),d(),s&&!i.providerExecuted&&await s({toolCall:i});break}case"tool-input-error":{i.dynamic?h({toolCallId:i.toolCallId,toolName:i.toolName,state:"output-error",input:i.input,errorText:i.errorText,providerMetadata:i.providerMetadata}):l({toolCallId:i.toolCallId,toolName:i.toolName,state:"output-error",input:void 0,rawInput:i.input,errorText:i.errorText,providerExecuted:i.providerExecuted,providerMetadata:i.providerMetadata}),d();break}case"tool-output-available":{if(i.dynamic){const p=g(i.toolCallId);h({toolCallId:i.toolCallId,toolName:p.toolName,state:"output-available",input:p.input,output:i.output,preliminary:i.preliminary})}else{const p=Z(i.toolCallId);l({toolCallId:i.toolCallId,toolName:Or(p),state:"output-available",input:p.input,output:i.output,providerExecuted:i.providerExecuted,preliminary:i.preliminary})}d();break}case"tool-output-error":{if(i.dynamic){const p=g(i.toolCallId);h({toolCallId:i.toolCallId,toolName:p.toolName,state:"output-error",input:p.input,errorText:i.errorText})}else{const p=Z(i.toolCallId);l({toolCallId:i.toolCallId,toolName:Or(p),state:"output-error",input:p.input,rawInput:p.rawInput,errorText:i.errorText})}d();break}case"start-step":{u.message.parts.push({type:"step-start"});break}case"finish-step":{u.activeTextParts={},u.activeReasoningParts={};break}case"start":{i.messageId!=null&&(u.message.id=i.messageId),await v(i.messageMetadata),(i.messageId!=null||i.messageMetadata!=null)&&d();break}case"finish":{await v(i.messageMetadata),i.messageMetadata!=null&&d();break}case"message-metadata":{await v(i.messageMetadata),i.messageMetadata!=null&&d();break}case"error":{a==null||a(new Error(i.errorText));break}default:if(Xc(i)){(r==null?void 0:r[i.type])!=null&&await Zr({value:i.data,schema:r[i.type]});const p=i;if(p.transient){o==null||o(p);break}const $=p.id!=null?u.message.parts.find(A=>p.type===A.type&&p.id===A.id):void 0;$!=null?$.data=p.data:u.message.parts.push(p),o==null||o(p),d()}}c.enqueue(i)})}}))}async function Ln({stream:t,onError:e}){const r=t.getReader();try{for(;;){const{done:n}=await r.read();if(n)break}}catch(n){e==null||e(n)}finally{r.releaseLock()}}Je({prefix:"aitxt",size:24});Je({prefix:"aiobj",size:24});function zr(t,e){if(t===e)return!0;if(t==null||e==null)return!1;if(typeof t!="object"&&typeof e!="object")return t===e;if(t.constructor!==e.constructor)return!1;if(t instanceof Date&&e instanceof Date)return t.getTime()===e.getTime();if(Array.isArray(t)){if(t.length!==e.length)return!1;for(let a=0;a<t.length;a++)if(!zr(t[a],e[a]))return!1;return!0}const r=Object.keys(t),n=Object.keys(e);if(r.length!==n.length)return!1;for(const a of r)if(!n.includes(a)||!zr(t[a],e[a]))return!1;return!0}var rl=class{constructor(){this.queue=[],this.isProcessing=!1}async processQueue(){if(!this.isProcessing){for(this.isProcessing=!0;this.queue.length>0;)await this.queue[0](),this.queue.shift();this.isProcessing=!1}}async run(t){return new Promise((e,r)=>{this.queue.push(async()=>{try{await t(),e()}catch(n){r(n)}}),this.processQueue()})}};Je({prefix:"aiobj",size:24});var nl={};Fc(nl,{object:()=>sl,text:()=>al});var al=()=>({type:"text",responseFormat:{type:"text"},async parsePartial({text:t}){return{partial:t}},async parseOutput({text:t}){return t}}),sl=({schema:t})=>{const e=Lc(t);return{type:"object",responseFormat:{type:"json",schema:e.jsonSchema},async parsePartial({text:r}){const n=await Dn(r);switch(n.state){case"failed-parse":case"undefined-input":return;case"repaired-parse":case"successful-parse":return{partial:n.value};default:{const a=n.state;throw new Error(`Unsupported parse state: ${a}`)}}},async parseOutput({text:r},n){const a=await ot({text:r});if(!a.success)throw new $r({message:"No object generated: could not parse the response.",cause:a.error,text:r,response:n.response,usage:n.usage,finishReason:n.finishReason});const s=await jt({value:a.value,schema:e});if(!s.success)throw new $r({message:"No object generated: response did not match schema.",cause:s.error,text:r,response:n.response,usage:n.usage,finishReason:n.finishReason});return s.value}}},il=Ie({name:m(),version:m()}),Lt=Ie({_meta:G(N({}).loose())}),Ue=Lt,ol=N({method:m(),params:G(Lt)}),ul=Ie({experimental:G(N({}).loose()),logging:G(N({}).loose()),prompts:G(Ie({listChanged:G(K())})),resources:G(Ie({subscribe:G(K()),listChanged:G(K())})),tools:G(Ie({listChanged:G(K())}))});Ue.extend({protocolVersion:m(),capabilities:ul,serverInfo:il,instructions:G(m())});var cl=Ue.extend({nextCursor:G(m())}),ll=N({name:m(),description:G(m()),inputSchema:N({type:I("object"),properties:G(N({}).loose())}).loose()}).loose();cl.extend({tools:le(ll)});var dl=N({type:I("text"),text:m()}).loose(),pl=N({type:I("image"),data:gn(),mimeType:m()}).loose(),Un=N({uri:m(),mimeType:G(m())}).loose(),fl=Un.extend({text:m()}),hl=Un.extend({blob:gn()}),ml=N({type:I("resource"),resource:Q([fl,hl])}).loose();Ue.extend({content:le(Q([dl,pl,ml])),isError:K().default(!1).optional()}).or(Ue.extend({toolResult:B()}));var dt="2.0",gl=N({jsonrpc:I(dt),id:Q([m(),Pe().int()])}).merge(ol).strict(),vl=N({jsonrpc:I(dt),id:Q([m(),Pe().int()]),result:Ue}).strict(),yl=N({jsonrpc:I(dt),id:Q([m(),Pe().int()]),error:N({code:Pe().int(),message:m(),data:G(B())})}).strict(),_l=N({jsonrpc:I(dt)}).merge(N({method:m(),params:G(Lt)})).strict();Q([gl,_l,vl,yl]);async function bl({stream:t,onTextPart:e}){const r=t.pipeThrough(new TextDecoderStream).getReader();for(;;){const{done:n,value:a}=await r.read();if(n)break;await e(a)}}var wl=()=>fetch;async function Ul({api:t,prompt:e,credentials:r,headers:n,body:a,streamProtocol:s="data",setCompletion:o,setLoading:i,setError:c,setAbortController:u,onFinish:d,onError:y,fetch:x=wl()}){var O;try{i(!0),c(void 0);const w=new AbortController;u(w),o("");const Z=await x(t,{method:"POST",body:JSON.stringify({prompt:e,...a}),credentials:r,headers:{"Content-Type":"application/json",...n},signal:w.signal}).catch(l=>{throw l});if(!Z.ok)throw new Error((O=await Z.text())!=null?O:"Failed to fetch the chat response.");if(!Z.body)throw new Error("The response body is empty.");let g="";switch(s){case"text":{await bl({stream:Z.body,onTextPart:l=>{g+=l,o(g)}});break}case"data":{await Ln({stream:$n({stream:Z.body,schema:Mn}).pipeThrough(new TransformStream({async transform(l){if(!l.success)throw l.error;const h=l.value;if(h.type==="text-delta")g+=h.delta,o(g);else if(h.type==="error")throw new Error(h.errorText)}})),onError:l=>{throw l}});break}default:{const l=s;throw new Error(`Unknown stream protocol: ${l}`)}}return d&&d(e,g),u(null),g}catch(w){if(w.name==="AbortError")return u(null),null;w instanceof Error&&y&&y(w),c(w)}finally{i(!1)}}async function kl(t){if(t==null)return[];if(!globalThis.FileList||!(t instanceof globalThis.FileList))throw new Error("FileList is not supported in the current environment");return Promise.all(Array.from(t).map(async e=>{const{name:r,type:n}=e,a=await new Promise((s,o)=>{const i=new FileReader;i.onload=c=>{var u;s((u=c.target)==null?void 0:u.result)},i.onerror=c=>o(c),i.readAsDataURL(e)});return{type:"file",mediaType:n,filename:r,url:a}}))}var xl=class{constructor({api:t="/api/chat",credentials:e,headers:r,body:n,fetch:a,prepareSendMessagesRequest:s,prepareReconnectToStreamRequest:o}){this.api=t,this.credentials=e,this.headers=r,this.body=n,this.fetch=a,this.prepareSendMessagesRequest=s,this.prepareReconnectToStreamRequest=o}async sendMessages({abortSignal:t,...e}){var r,n,a,s,o;const i=await ke(this.body),c=await ke(this.headers),u=await ke(this.credentials),d=await((r=this.prepareSendMessagesRequest)==null?void 0:r.call(this,{api:this.api,id:e.chatId,messages:e.messages,body:{...i,...e.body},headers:{...c,...e.headers},credentials:u,requestMetadata:e.metadata,trigger:e.trigger,messageId:e.messageId})),y=(n=d==null?void 0:d.api)!=null?n:this.api,x=(d==null?void 0:d.headers)!==void 0?d.headers:{...c,...e.headers},O=(d==null?void 0:d.body)!==void 0?d.body:{...i,...e.body,id:e.chatId,messages:e.messages,trigger:e.trigger,messageId:e.messageId},w=(a=d==null?void 0:d.credentials)!=null?a:u,g=await((s=this.fetch)!=null?s:globalThis.fetch)(y,{method:"POST",headers:{"Content-Type":"application/json",...x},body:JSON.stringify(O),credentials:w,signal:t});if(!g.ok)throw new Error((o=await g.text())!=null?o:"Failed to fetch the chat response.");if(!g.body)throw new Error("The response body is empty.");return this.processResponseStream(g.body)}async reconnectToStream(t){var e,r,n,a,s;const o=await ke(this.body),i=await ke(this.headers),c=await ke(this.credentials),u=await((e=this.prepareReconnectToStreamRequest)==null?void 0:e.call(this,{api:this.api,id:t.chatId,body:{...o,...t.body},headers:{...i,...t.headers},credentials:c,requestMetadata:t.metadata})),d=(r=u==null?void 0:u.api)!=null?r:`${this.api}/${t.chatId}/stream`,y=(u==null?void 0:u.headers)!==void 0?u.headers:{...i,...t.headers},x=(n=u==null?void 0:u.credentials)!=null?n:c,w=await((a=this.fetch)!=null?a:globalThis.fetch)(d,{method:"GET",headers:y,credentials:x});if(w.status===204)return null;if(!w.ok)throw new Error((s=await w.text())!=null?s:"Failed to fetch the chat response.");if(!w.body)throw new Error("The response body is empty.");return this.processResponseStream(w.body)}},Il=class extends xl{constructor(t={}){super(t)}processResponseStream(t){return $n({stream:t,schema:Mn}).pipeThrough(new TransformStream({async transform(e,r){if(!e.success)throw e.error;r.enqueue(e.value)}}))}},Fl=class{constructor({generateId:t=Sc,id:e=t(),transport:r=new Il,messageMetadataSchema:n,dataPartSchemas:a,state:s,onError:o,onToolCall:i,onFinish:c,onData:u,sendAutomaticallyWhen:d}){this.activeResponse=void 0,this.jobExecutor=new rl,this.sendMessage=async(y,x)=>{var O,w,Z,g;if(y==null){await this.makeRequest({trigger:"submit-message",messageId:(O=this.lastMessage)==null?void 0:O.id,...x});return}let l;if("text"in y||"files"in y?l={parts:[...Array.isArray(y.files)?y.files:await kl(y.files),..."text"in y&&y.text!=null?[{type:"text",text:y.text}]:[]]}:l=y,y.messageId!=null){const h=this.state.messages.findIndex(v=>v.id===y.messageId);if(h===-1)throw new Error(`message with id ${y.messageId} not found`);if(this.state.messages[h].role!=="user")throw new Error(`message with id ${y.messageId} is not a user message`);this.state.messages=this.state.messages.slice(0,h+1),this.state.replaceMessage(h,{...l,id:y.messageId,role:(w=l.role)!=null?w:"user",metadata:y.metadata})}else this.state.pushMessage({...l,id:(Z=l.id)!=null?Z:this.generateId(),role:(g=l.role)!=null?g:"user",metadata:y.metadata});await this.makeRequest({trigger:"submit-message",messageId:y.messageId,...x})},this.regenerate=async({messageId:y,...x}={})=>{const O=y==null?this.state.messages.length-1:this.state.messages.findIndex(w=>w.id===y);if(O===-1)throw new Error(`message ${y} not found`);this.state.messages=this.state.messages.slice(0,this.messages[O].role==="assistant"?O:O+1),await this.makeRequest({trigger:"regenerate-message",messageId:y,...x})},this.resumeStream=async(y={})=>{await this.makeRequest({trigger:"resume-stream",...y})},this.clearError=()=>{this.status==="error"&&(this.state.error=void 0,this.setStatus({status:"ready"}))},this.addToolResult=async({tool:y,toolCallId:x,output:O})=>this.jobExecutor.run(async()=>{var w,Z;const g=this.state.messages,l=g[g.length-1];this.state.replaceMessage(g.length-1,{...l,parts:l.parts.map(h=>Ne(h)&&h.toolCallId===x?{...h,state:"output-available",output:O}:h)}),this.activeResponse&&(this.activeResponse.state.message.parts=this.activeResponse.state.message.parts.map(h=>Ne(h)&&h.toolCallId===x?{...h,state:"output-available",output:O,errorText:void 0}:h)),this.status!=="streaming"&&this.status!=="submitted"&&((w=this.sendAutomaticallyWhen)!=null&&w.call(this,{messages:this.state.messages}))&&this.makeRequest({trigger:"submit-message",messageId:(Z=this.lastMessage)==null?void 0:Z.id})}),this.stop=async()=>{var y;this.status!=="streaming"&&this.status!=="submitted"||(y=this.activeResponse)!=null&&y.abortController&&this.activeResponse.abortController.abort()},this.id=e,this.transport=r,this.generateId=t,this.messageMetadataSchema=n,this.dataPartSchemas=a,this.state=s,this.onError=o,this.onToolCall=i,this.onFinish=c,this.onData=u,this.sendAutomaticallyWhen=d}get status(){return this.state.status}setStatus({status:t,error:e}){this.status!==t&&(this.state.status=t,this.state.error=e)}get error(){return this.state.error}get messages(){return this.state.messages}get lastMessage(){return this.state.messages[this.state.messages.length-1]}set messages(t){this.state.messages=t}async makeRequest({trigger:t,metadata:e,headers:r,body:n,messageId:a}){var s,o,i;this.setStatus({status:"submitted",error:void 0});const c=this.lastMessage;try{const u={state:el({lastMessage:this.state.snapshot(c),messageId:this.generateId()}),abortController:new AbortController};this.activeResponse=u;let d;if(t==="resume-stream"){const x=await this.transport.reconnectToStream({chatId:this.id,metadata:e,headers:r,body:n});if(x==null){this.setStatus({status:"ready"});return}d=x}else d=await this.transport.sendMessages({chatId:this.id,messages:this.state.messages,abortSignal:u.abortController.signal,metadata:e,headers:r,body:n,trigger:t,messageId:a});const y=x=>this.jobExecutor.run(()=>x({state:u.state,write:()=>{var O;this.setStatus({status:"streaming"}),u.state.message.id===((O=this.lastMessage)==null?void 0:O.id)?this.state.replaceMessage(this.state.messages.length-1,u.state.message):this.state.pushMessage(u.state.message)}}));await Ln({stream:tl({stream:d,onToolCall:this.onToolCall,onData:this.onData,messageMetadataSchema:this.messageMetadataSchema,dataPartSchemas:this.dataPartSchemas,runUpdateMessageJob:y,onError:x=>{throw x}}),onError:x=>{throw x}}),(s=this.onFinish)==null||s.call(this,{message:u.state.message}),this.setStatus({status:"ready"})}catch(u){if(u.name==="AbortError")return this.setStatus({status:"ready"}),null;this.onError&&u instanceof Error&&this.onError(u),this.setStatus({status:"error",error:u})}finally{this.activeResponse=void 0}(o=this.sendAutomaticallyWhen)!=null&&o.call(this,{messages:this.state.messages})&&await this.makeRequest({trigger:"submit-message",messageId:(i=this.lastMessage)==null?void 0:i.id,metadata:e,headers:r,body:n})}},Sl=N({type:I("text"),text:m(),state:lt(["streaming","done"]).optional(),providerMetadata:P.optional()}),Tl=N({type:I("reasoning"),text:m(),state:lt(["streaming","done"]).optional(),providerMetadata:P.optional()}),El=N({type:I("source-url"),sourceId:m(),url:m(),title:m().optional(),providerMetadata:P.optional()}),Zl=N({type:I("source-document"),sourceId:m(),mediaType:m(),title:m(),filename:m().optional(),providerMetadata:P.optional()}),$l=N({type:I("file"),mediaType:m(),filename:m().optional(),url:m(),providerMetadata:P.optional()}),Ol=N({type:I("step-start")}),zl=N({type:m().startsWith("data-"),id:m().optional(),data:B()}),Al=[N({type:I("dynamic-tool"),toolName:m(),toolCallId:m(),state:I("input-streaming"),input:B().optional(),output:te().optional(),errorText:te().optional()}),N({type:I("dynamic-tool"),toolName:m(),toolCallId:m(),state:I("input-available"),input:B(),output:te().optional(),errorText:te().optional(),callProviderMetadata:P.optional()}),N({type:I("dynamic-tool"),toolName:m(),toolCallId:m(),state:I("output-available"),input:B(),output:B(),errorText:te().optional(),callProviderMetadata:P.optional(),preliminary:K().optional()}),N({type:I("dynamic-tool"),toolName:m(),toolCallId:m(),state:I("output-error"),input:B(),output:te().optional(),errorText:m(),callProviderMetadata:P.optional()})],Nl=[N({type:m().startsWith("tool-"),toolCallId:m(),state:I("input-streaming"),input:B().optional(),output:te().optional(),errorText:te().optional()}),N({type:m().startsWith("tool-"),toolCallId:m(),state:I("input-available"),input:B(),output:te().optional(),errorText:te().optional(),callProviderMetadata:P.optional()}),N({type:m().startsWith("tool-"),toolCallId:m(),state:I("output-available"),input:B(),output:B(),errorText:te().optional(),callProviderMetadata:P.optional(),preliminary:K().optional()}),N({type:m().startsWith("tool-"),toolCallId:m(),state:I("output-error"),input:B(),output:te().optional(),errorText:m(),callProviderMetadata:P.optional()})];N({id:m(),role:lt(["system","user","assistant"]),metadata:B().optional(),parts:le(Q([Sl,Tl,El,Zl,$l,Ol,zl,...Al,...Nl]))});var Cl=Fn("<textarea></textarea>");function Vl(t,e){Yn(e,!0);let r=Ut(e,"ref",15,null),n=Ut(e,"value",15),a=ra(e,["$$slots","$$events","$$legacy","ref","value","class"]);var s=Cl();Xn(s);let o;ta(s,i=>r(i),()=>r()),Kn(i=>o=ea(s,o,{class:i,...a}),[()=>Qn("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e.class)]),na(s,n),Vn(t,s),Gn()}export{Fl as A,Vl as T,Lc as a,Ll as b,Ul as c,le as d,m as e,Sc as g,zr as i,N as o,Dn as p,jt as s};
