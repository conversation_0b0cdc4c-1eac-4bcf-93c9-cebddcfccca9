import { d as copy_payload, f as assign_payload, c as pop, p as push, g as ensure_array_like, h as attr_class, j as stringify, e as escape_html } from "../../../chunks/context.js";
import { A as Arrow_up } from "../../../chunks/arrow-up.js";
import { B as Button } from "../../../chunks/button.js";
import { T as Textarea } from "../../../chunks/textarea.js";
import { S as StreamingMarkdown } from "../../../chunks/StreamingMarkdown.js";
import { C as Chat } from "../../../chunks/chat.svelte.js";
function _page($$payload, $$props) {
  push();
  const chat = new Chat({
    async onToolCall({ toolCall }) {
      await new Promise((resolve) => setTimeout(resolve, 2e3));
      if (toolCall.toolName === "getLocation") {
        const cities = [
          "New York",
          "Los Angeles",
          "Chicago",
          "San Francisco"
        ];
        const location = cities[Math.floor(Math.random() * cities.length)];
        await chat.addToolResult({
          toolCallId: toolCall.toolCallId,
          tool: "getLocation",
          output: location
        });
      }
    }
  });
  let input = "";
  const disabled = chat.status !== "ready";
  function mapRoleToClass(role) {
    return role === "assistant" ? "bg-primary text-secondary rounded-md" : "bg-secondary text-primary rounded-md justify-self-end";
  }
  function handleSubmit(e) {
    e.preventDefault();
    chat.sendMessage({ text: input });
    input = "";
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    const each_array = ensure_array_like(chat.messages);
    $$payload2.out += `<main class="flex flex-col items-center h-dvh w-dvw"><div class="grid h-full w-full max-w-4xl grid-cols-1 grid-rows-[1fr,120px] p-2"><div class="overflow-y-auto w-full h-full"><!--[-->`;
    for (let $$index_1 = 0, $$length = each_array.length; $$index_1 < $$length; $$index_1++) {
      let message = each_array[$$index_1];
      const each_array_1 = ensure_array_like(message.parts);
      $$payload2.out += `<div${attr_class(`${stringify(mapRoleToClass(message.role))} my-2 max-w-[80%] p-2 flex flex-col gap-2`)}><!--[-->`;
      for (let i = 0, $$length2 = each_array_1.length; i < $$length2; i++) {
        let part = each_array_1[i];
        if (part.type === "text") {
          $$payload2.out += "<!--[-->";
          if (message.role === "assistant") {
            $$payload2.out += "<!--[-->";
            StreamingMarkdown($$payload2, {
              content: part.text,
              animationDelay: 20,
              fadeInDuration: 400
            });
          } else {
            $$payload2.out += "<!--[!-->";
            $$payload2.out += `${escape_html(part.text)}`;
          }
          $$payload2.out += `<!--]-->`;
        } else if (part.type === "tool-askForConfirmation") {
          $$payload2.out += "<!--[1-->";
          const toolCallId = part.toolCallId;
          const state = part.state;
          if (state === "input-available") {
            $$payload2.out += "<!--[-->";
            const input2 = part.input;
            $$payload2.out += `<div class="flex flex-col gap-2">${escape_html(input2.message)} <div class="flex gap-2">`;
            Button($$payload2, {
              variant: "default",
              onclick: () => chat.addToolResult({
                toolCallId,
                tool: "askForConfirmation",
                output: "Yes, confirmed"
              }),
              children: ($$payload3) => {
                $$payload3.out += `<!---->Yes`;
              },
              $$slots: { default: true }
            });
            $$payload2.out += `<!----> `;
            Button($$payload2, {
              variant: "secondary",
              onclick: () => chat.addToolResult({
                toolCallId,
                tool: "askForConfirmation",
                output: "No, denied"
              }),
              children: ($$payload3) => {
                $$payload3.out += `<!---->No`;
              },
              $$slots: { default: true }
            });
            $$payload2.out += `<!----></div></div>`;
          } else if (state === "output-available") {
            $$payload2.out += "<!--[1-->";
            $$payload2.out += `<div class="text-gray-500">${escape_html(part.output)}</div>`;
          } else {
            $$payload2.out += "<!--[!-->";
          }
          $$payload2.out += `<!--]-->`;
        } else if (part.type === "tool-getLocation") {
          $$payload2.out += "<!--[2-->";
          if (part.state === "input-available") {
            $$payload2.out += "<!--[-->";
            $$payload2.out += `<div class="text-gray-500">Getting location...</div>`;
          } else if (part.state === "output-available") {
            $$payload2.out += "<!--[1-->";
            $$payload2.out += `<div class="text-gray-500">Location: ${escape_html(part.output)}</div>`;
          } else {
            $$payload2.out += "<!--[!-->";
          }
          $$payload2.out += `<!--]-->`;
        } else if (part.type === "tool-getWeatherInformation") {
          $$payload2.out += "<!--[3-->";
          if (part.state === "input-streaming") {
            $$payload2.out += "<!--[-->";
            $$payload2.out += `<pre>${escape_html(JSON.stringify(part, null, 2))}</pre>`;
          } else if (part.state === "input-available") {
            $$payload2.out += "<!--[1-->";
            const input2 = part.input;
            $$payload2.out += `<div class="text-gray-500">Getting weather information for ${escape_html(input2.city)}...</div>`;
          } else if (part.state === "output-available") {
            $$payload2.out += "<!--[2-->";
            const input2 = part.input;
            $$payload2.out += `<div class="text-gray-500">Weather in ${escape_html(input2.city)}: ${escape_html(part.output)}</div>`;
          } else {
            $$payload2.out += "<!--[!-->";
          }
          $$payload2.out += `<!--]-->`;
        } else {
          $$payload2.out += "<!--[!-->";
        }
        $$payload2.out += `<!--]-->`;
      }
      $$payload2.out += `<!--]--></div>`;
    }
    $$payload2.out += `<!--]--></div> <form class="relative"><p>${escape_html(chat.status)}</p> <div><a href="/chat/1">chat 1</a> <a href="/chat/2">chat 2</a> <a href="/chat/3">chat 3</a></div> `;
    Textarea($$payload2, {
      placeholder: "Send a message...",
      class: "h-full",
      onkeydown: (event) => {
        if (event.key === "Enter" && !event.shiftKey) {
          event.preventDefault();
          handleSubmit(event);
        }
      },
      get value() {
        return input;
      },
      set value($$value) {
        input = $$value;
        $$settled = false;
      }
    });
    $$payload2.out += `<!----> `;
    Button($$payload2, {
      "aria-label": "Send message",
      disabled,
      type: "submit",
      size: "icon",
      class: "absolute right-3 bottom-3",
      children: ($$payload3) => {
        Arrow_up($$payload3, {});
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----></form></div></main>`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}
export {
  _page as default
};
