import "clsx";
import { c as createContext, K as KeyedStore, S as SvelteMap } from "./utils.svelte.js";
class CompletionStore {
  completions = new SvelteMap();
  data = [];
  loading = false;
  error;
}
class KeyedCompletionStore extends KeyedStore {
  constructor(value) {
    super(CompletionStore, value);
  }
}
const {
  hasContext: hasCompletionContext,
  getContext: getCompletionContext,
  setContext: setCompletionContext
} = createContext("Completion");
export {
  KeyedCompletionStore as K,
  getCompletionContext as g,
  hasCompletionContext as h,
  setCompletionContext as s
};
