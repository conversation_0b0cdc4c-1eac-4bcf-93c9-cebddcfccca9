import{K as l,N as u,ar as h,_ as m,as as E,at as g,C as f,D as s,M as T,O as N}from"./Cc9lkQ6R.js";function p(r){var t=document.createElement("template");return t.innerHTML=r,t.content}function a(r,t){var e=m;e.nodes_start===null&&(e.nodes_start=r,e.nodes_end=t)}function M(r,t){var e=(t&E)!==0,_=(t&g)!==0,n,d=!r.startsWith("<!>");return()=>{if(f)return a(s,null),s;n===void 0&&(n=p(d?r:"<!>"+r),e||(n=u(n)));var o=_||h?document.importNode(n,!0):n.cloneNode(!0);if(e){var c=u(o),i=o.lastChild;a(c,i)}else a(o,o);return o}}function x(r,t,e="svg"){var _=!r.startsWith("<!>"),n=`<${e}>${_?r:"<!>"+r}</${e}>`,d;return()=>{if(f)return a(s,null),s;if(!d){var o=p(n),c=u(o);d=u(c)}var i=d.cloneNode(!0);return a(i,i),i}}function C(r=""){if(!f){var t=l(r+"");return a(t,t),t}var e=s;return e.nodeType!==3&&(e.before(e=l()),T(e)),a(e,e),e}function L(){if(f)return a(s,null),s;var r=document.createDocumentFragment(),t=document.createComment(""),e=l();return r.append(t,e),a(t,e),r}function O(r,t){if(f){m.nodes_end=s,N();return}r!==null&&r.before(t)}const w="5";var v;typeof window<"u"&&((v=window.__svelte??(window.__svelte={})).v??(v.v=new Set)).add(w);export{O as a,C as b,L as c,a as d,x as n,M as t};
