import{t as j,a as c,b as p}from"../chunks/CGanT4Ze.js";import{t as A,g as s,d as k,c as n,s as o,b as a,n as m,r as i}from"../chunks/Cc9lkQ6R.js";import{s as B}from"../chunks/2Hh1nz_V.js";import{S as C}from"../chunks/BjpmZ2ke.js";import{B as g}from"../chunks/BrOQj_DL.js";var P=j('<main class="container mx-auto p-6 max-w-4xl svelte-ljbuca"><div class="mb-8"><h1 class="text-3xl font-bold mb-4">流式 Markdown 渲染演示</h1> <p class="text-gray-600 mb-6">这个演示展示了如何使用 Svelte 5 + Vercel AI SDK 实现 Markdown 的流式渲染和逐字淡入效果。</p> <div class="flex gap-4 mb-6"><!> <!> <!></div></div> <div class="border rounded-lg p-6 bg-white shadow-sm"><h2 class="text-xl font-semibold mb-4">渲染结果：</h2> <div class="min-h-[200px]"><!></div></div> <div class="mt-8 p-4 bg-gray-50 rounded-lg"><h3 class="text-lg font-semibold mb-2">技术说明：</h3> <ul class="list-disc list-inside space-y-1 text-sm text-gray-700"><li>使用 <code class="svelte-ljbuca">marked</code> 库进行 Markdown 解析</li> <li>通过 CSS 动画实现逐字淡入效果</li> <li>支持增量内容更新以提高性能</li> <li>兼容 Svelte 5 的新语法（$props, $state, $effect）</li> <li>可配置动画延迟和持续时间</li></ul></div></main>');function q(_){let l=k(""),t=k(!1);const v=`# 流式 Markdown 演示

这是一个**流式 Markdown 渲染**组件的演示。

## 功能特点

- 支持实时 Markdown 解析
- 逐字淡入动画效果
- 高性能增量更新
- 支持所有标准 Markdown 语法

### 代码示例

\`\`\`javascript
function streamingMarkdown() {
  console.log('Hello, streaming world!');
  return 'Amazing!';
}
\`\`\`

### 列表支持

1. 有序列表项 1
2. 有序列表项 2
3. 有序列表项 3

- 无序列表项 A
- 无序列表项 B
- 无序列表项 C

### 引用块

> 这是一个引用块的示例。
> 它可以包含多行内容。
> 
> 甚至可以包含**粗体**和*斜体*文本。

### 表格

| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |
| 数据4 | 数据5 | 数据6 |

### 链接和图片

这里有一个[链接示例](https://example.com)。

---

**演示完成！** 🎉`;function M(){if(s(t))return;a(t,!0),a(l,"");let e=0;const d=setInterval(()=>{e<v.length?(a(l,s(l)+v[e]),e++):(clearInterval(d),a(t,!1))},50)}function S(){a(l,""),a(t,!1)}function y(){a(l,v),a(t,!1)}var u=P(),f=n(u),x=o(n(f),4),b=n(x);g(b,{onclick:M,get disabled(){return s(t)},variant:"default",children:(e,d)=>{m();var r=p();A(()=>B(r,s(t)?"流式渲染中...":"开始流式演示")),c(e,r)},$$slots:{default:!0}});var h=o(b,2);g(h,{onclick:y,get disabled(){return s(t)},variant:"secondary",children:(e,d)=>{m();var r=p("显示完整内容");c(e,r)},$$slots:{default:!0}});var D=o(h,2);g(D,{onclick:S,get disabled(){return s(t)},variant:"outline",children:(e,d)=>{m();var r=p("重置");c(e,r)},$$slots:{default:!0}}),i(x),i(f);var $=o(f,2),w=o(n($),2),I=n(w);C(I,{get content(){return s(l)},animationDelay:30,fadeInDuration:500}),i(w),i($),m(2),i(u),c(_,u)}export{q as component};
