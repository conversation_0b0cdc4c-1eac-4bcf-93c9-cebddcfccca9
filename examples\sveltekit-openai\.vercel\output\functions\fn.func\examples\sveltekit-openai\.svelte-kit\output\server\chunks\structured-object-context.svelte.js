import "clsx";
import { c as createContext, K as KeyedStore } from "./utils.svelte.js";
class StructuredObjectStore {
  object;
  loading = false;
  error;
}
class KeyedStructuredObjectStore extends KeyedStore {
  constructor(value) {
    super(StructuredObjectStore, value);
  }
}
const {
  hasContext: hasStructuredObjectContext,
  getContext: getStructuredObjectContext,
  setContext: setStructuredObjectContext
} = createContext("StructuredObject");
export {
  KeyedStructuredObjectStore as K,
  getStructuredObjectContext as g,
  hasStructuredObjectContext as h,
  setStructuredObjectContext as s
};
