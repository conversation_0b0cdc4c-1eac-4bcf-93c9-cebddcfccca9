import{t as g,a as o,c as R,b as O}from"../chunks/CGanT4Ze.js";import{p as ma,t as _,a as pa,c as d,b as X,g as a,s as $,d as _a,r as i,u as A,f as M,n as Z}from"../chunks/Cc9lkQ6R.js";import{e as ga,s as y}from"../chunks/2Hh1nz_V.js";import{i as f}from"../chunks/0U5e1eCd.js";import{e as aa,A as xa,i as ha}from"../chunks/zbNWLoGC.js";import{s as ya}from"../chunks/krzcCheT.js";import{p as ba}from"../chunks/Bq-mu1H_.js";import{B as q}from"../chunks/BrOQj_DL.js";import{T as wa}from"../chunks/CEXehBSS.js";import{C as Ca}from"../chunks/U6ZYfsmc.js";var ka=g('<div class="flex flex-col gap-2"> <div class="flex gap-2"><!> <!></div></div>'),Ta=g('<div class="text-gray-500"> </div>'),Ia=g('<div class="text-gray-500">Getting location...</div>'),Sa=g('<div class="text-gray-500"> </div>'),La=g("<pre> </pre>"),Na=g('<div class="text-gray-500"> </div>'),$a=g('<div class="text-gray-500"> </div>'),Fa=g("<div></div>"),Pa=g('<main class="flex flex-col items-center h-dvh w-dvw"><div class="grid h-full w-full max-w-4xl grid-cols-1 grid-rows-[1fr,120px] p-2"><div class="overflow-y-auto w-full h-full"></div> <form class="relative"><p> </p> <div><a href="/chat/1">chat 1</a> <a href="/chat/2">chat 2</a> <a href="/chat/3">chat 3</a></div> <!> <!></form></div></main>');function Ea(ta,ea){ma(ea,!0);const b=new Ca({id:ba.params.id,async onToolCall({toolCall:e}){if(await new Promise(x=>setTimeout(x,2e3)),e.toolName==="getLocation"){const x=["New York","Los Angeles","Chicago","San Francisco"],w=x[Math.floor(Math.random()*x.length)];await b.addToolResult({toolCallId:e.toolCallId,tool:"getLocation",output:w})}}}),oa=A(()=>b.status!=="ready");function sa(e){return e==="assistant"?"bg-primary text-secondary rounded-md":"bg-secondary text-primary rounded-md justify-self-end"}let I=_a("");function H(e){console.log("handleSubmit",e),console.log("input",a(I)),e.preventDefault(),b.sendMessage({text:a(I)}),X(I,"")}var Y=Pa(),Q=d(Y),B=d(Q);aa(B,21,()=>b.messages,e=>e.id,(e,x)=>{var w=Fa();aa(w,21,()=>a(x).parts,ha,(W,t)=>{var V=R(),la=M(V);{var va=C=>{var F=O();_(()=>y(F,a(t).text)),o(C,F)},na=(C,F)=>{{var da=k=>{var P=R(),j=M(P);{var z=l=>{var c=ka();const m=A(()=>a(t).input);var v=d(c),s=$(v),u=d(s);q(u,{variant:"default",onclick:()=>b.addToolResult({toolCallId:a(t).toolCallId,tool:"askForConfirmation",output:"Yes, confirmed"}),children:(r,n)=>{Z();var p=O("Yes");o(r,p)},$$slots:{default:!0}});var S=$(u,2);q(S,{variant:"secondary",onclick:()=>b.addToolResult({toolCallId:a(t).toolCallId,tool:"askForConfirmation",output:"No, denied"}),children:(r,n)=>{Z();var p=O("No");o(r,p)},$$slots:{default:!0}}),i(s),i(c),_(()=>y(v,`${a(m).message??""} `)),o(l,c)},h=(l,c)=>{{var m=v=>{var s=Ta(),u=d(s,!0);i(s),_(()=>y(u,a(t).output)),o(v,s)};f(l,v=>{a(t).state==="output-available"&&v(m)},c)}};f(j,l=>{a(t).state==="input-available"?l(z):l(h,!1)})}o(k,P)},fa=(k,P)=>{{var j=h=>{var l=R(),c=M(l);{var m=s=>{var u=Ia();o(s,u)},v=(s,u)=>{{var S=r=>{var n=Sa(),p=d(n);i(n),_(()=>y(p,`Location: ${a(t).output??""}`)),o(r,n)};f(s,r=>{a(t).state==="output-available"&&r(S)},u)}};f(c,s=>{a(t).state==="input-available"?s(m):s(v,!1)})}o(h,l)},z=(h,l)=>{{var c=m=>{var v=R(),s=M(v);{var u=r=>{var n=La(),p=d(n,!0);i(n),_(E=>y(p,E),[()=>JSON.stringify(a(t),null,2)]),o(r,n)},S=(r,n)=>{{var p=T=>{var L=Na();const J=A(()=>a(t).input);var N=d(L);i(L),_(()=>y(N,`Getting weather information for ${a(J).city??""}...`)),o(T,L)},E=(T,L)=>{{var J=N=>{var K=$a();const ua=A(()=>a(t).input);var ca=d(K);i(K),_(()=>y(ca,`Weather in ${a(ua).city??""}: ${a(t).output??""}`)),o(N,K)};f(T,N=>{a(t).state==="output-available"&&N(J)},L)}};f(r,T=>{a(t).state==="input-available"?T(p):T(E,!1)},n)}};f(s,r=>{a(t).state==="input-streaming"?r(u):r(S,!1)})}o(m,v)};f(h,m=>{a(t).type==="tool-getWeatherInformation"&&m(c)},l)}};f(k,h=>{a(t).type==="tool-getLocation"?h(j):h(z,!1)},P)}};f(C,k=>{a(t).type==="tool-askForConfirmation"?k(da):k(fa,!1)},F)}};f(la,C=>{a(t).type==="text"?C(va):C(na,!1)})}o(W,V)}),i(w),_(W=>ya(w,1,`${W??""} my-2 max-w-[80%] p-2 flex flex-col gap-2`),[()=>sa(a(x).role)]),o(e,w)}),i(B);var D=$(B,2),G=d(D),ra=d(G,!0);i(G);var U=$(G,4);wa(U,{placeholder:"Send a message...",class:"h-full",onkeydown:e=>{e.key==="Enter"&&!e.shiftKey&&(e.preventDefault(),H(e))},get value(){return a(I)},set value(e){X(I,e,!0)}});var ia=$(U,2);q(ia,{"aria-label":"Send message",get disabled(){return a(oa)},type:"submit",size:"icon",class:"absolute right-3 bottom-3",children:(e,x)=>{xa(e,{})},$$slots:{default:!0}}),i(D),i(Q),i(Y),_(()=>y(ra,b.status)),ga("submit",D,H),o(ta,Y),pa()}export{Ea as component};
