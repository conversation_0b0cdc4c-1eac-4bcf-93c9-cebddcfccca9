import{t as n,a as r,c as Q,b as ut}from"../chunks/CGanT4Ze.js";import{p as Mt,t as k,a as jt,s as p,b as Z,g as t,d as Tt,c as s,u as j,r as e,f as J,n as ct}from"../chunks/Cc9lkQ6R.js";import{d as zt,e as Dt,s as y}from"../chunks/2Hh1nz_V.js";import{i as d}from"../chunks/0U5e1eCd.js";import{e as q,A as Et,i as ft}from"../chunks/zbNWLoGC.js";import{s as U}from"../chunks/krzcCheT.js";import{B as tt}from"../chunks/BrOQj_DL.js";import{T as Ft}from"../chunks/CEXehBSS.js";import{S as Pt}from"../chunks/BjpmZ2ke.js";import{C as Rt}from"../chunks/U6ZYfsmc.js";var Bt=(at,W,u)=>W(t(u)),Qt=n('<button class="p-3 text-left bg-white border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors"><div class="text-sm text-gray-700"> </div></button>'),Jt=n('<div class="text-center py-12"><div class="mb-8"><h2 class="text-xl font-semibold text-gray-700 mb-2">开始对话</h2> <p class="text-gray-500">AI 的回复将以流式 Markdown 格式呈现，支持逐字淡入动画</p></div> <div class="grid grid-cols-1 md:grid-cols-2 gap-3 max-w-2xl mx-auto"></div></div>'),Kt=n('<div class="prose prose-sm max-w-none"><!></div>'),Lt=n('<div class="whitespace-pre-wrap"> </div>'),Nt=n('<div class="flex flex-col gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded"><div class="text-yellow-800"> </div> <div class="flex gap-2"><!> <!></div></div>'),Ut=n('<div class="text-gray-600 text-sm"> </div>'),Wt=n('<div class="text-blue-600 text-sm">正在获取位置信息...</div>'),Yt=n('<div class="text-blue-600 text-sm"> </div>'),Gt=n('<div class="text-green-600 text-sm"> </div>'),Ht=n('<div class="text-green-600 text-sm"> </div>'),Ot=n('<div><div><div class="flex items-center mb-2"><div> </div> <span> </span></div> <!></div></div>'),Vt=n('<div class="flex justify-start"><div class="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-[85%]"><div class="flex items-center space-x-2"><div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div> <div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style="animation-delay: 0.1s"></div> <div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style="animation-delay: 0.2s"></div> <span class="text-blue-700 text-sm ml-2">AI 正在思考...</span></div></div></div>'),Xt=n('<main class="flex flex-col h-screen bg-gray-50"><header class="bg-white border-b border-gray-200 p-4"><div class="max-w-4xl mx-auto"><h1 class="text-2xl font-bold text-gray-900">流式 Markdown AI 聊天</h1> <p class="text-gray-600 mt-1">体验 AI 回复的实时 Markdown 渲染和逐字淡入效果</p></div></header> <div class="flex-1 max-w-4xl mx-auto w-full flex flex-col"><div class="flex-1 overflow-y-auto p-4 space-y-4"><!> <!> <!></div> <div class="border-t border-gray-200 bg-white p-4"><form class="relative max-w-4xl mx-auto"><!> <!></form> <div class="text-center mt-2"><span class="text-xs text-gray-500"> </span></div></div></div></main>');function va(at,W){Mt(W,!0);const u=new Rt({api:"/api/chat"});let S=Tt("");const et=j(()=>u.status!=="ready");function xt(a){return a==="assistant"?"bg-blue-50 text-blue-900 rounded-lg border border-blue-200":"bg-gray-50 text-gray-900 rounded-lg border border-gray-200 justify-self-end"}function Y(a){a.preventDefault(),t(S).trim()&&(u.sendMessage({text:t(S)}),Z(S,""))}const mt=["请用 Markdown 格式介绍一下 Svelte 5 的新特性","写一个关于 JavaScript 异步编程的教程，包含代码示例","创建一个包含表格和列表的项目计划文档","解释什么是响应式编程，并提供示例代码"];function bt(a){Z(S,a,!0),Y(new Event("submit"))}var G=Xt(),st=p(s(G),2),H=s(st),rt=s(H);{var pt=a=>{var i=Jt(),$=p(s(i),2);q($,21,()=>mt,ft,(E,T)=>{var _=Qt();_.__click=[Bt,bt,T];var L=s(_),F=s(L,!0);e(L),e(_),k(()=>{_.disabled=t(et),y(F,t(T))}),r(E,_)}),e($),e(i),r(a,i)};d(rt,a=>{u.messages.length===0&&a(pt)})}var ot=p(rt,2);q(ot,17,()=>u.messages,a=>a.id,(a,i)=>{var $=Ot(),E=s($),T=s(E),_=s(T),L=s(_,!0);e(_);var F=p(_,2),kt=s(F,!0);e(F),e(T);var It=p(T,2);q(It,17,()=>t(i).parts,ft,(O,o)=>{var nt=Q(),Ct=J(nt);{var At=z=>{var N=Q(),V=J(N);{var X=b=>{var c=Kt(),C=s(c);Pt(C,{get content(){return t(o).text},animationDelay:15,fadeInDuration:300}),e(c),r(b,c)},I=b=>{var c=Lt(),C=s(c,!0);e(c),k(()=>y(C,t(o).text)),r(b,c)};d(V,b=>{t(i).role==="assistant"?b(X):b(I,!1)})}r(z,N)},St=(z,N)=>{{var V=I=>{var b=Q();const c=j(()=>t(o).toolCallId),C=j(()=>t(o).state);var A=J(b);{var D=f=>{var g=Nt();const x=j(()=>t(o).input);var v=s(g),h=s(v,!0);e(v);var l=p(v,2),m=s(l);tt(m,{variant:"default",size:"sm",onclick:()=>u.addToolResult({toolCallId:t(c),tool:"askForConfirmation",output:"Yes, confirmed"}),children:(w,R)=>{ct();var B=ut("确认");r(w,B)},$$slots:{default:!0}});var M=p(m,2);tt(M,{variant:"secondary",size:"sm",onclick:()=>u.addToolResult({toolCallId:t(c),tool:"askForConfirmation",output:"No, denied"}),children:(w,R)=>{ct();var B=ut("取消");r(w,B)},$$slots:{default:!0}}),e(l),e(g),k(()=>y(h,t(x).message)),r(f,g)},P=(f,g)=>{{var x=v=>{var h=Ut(),l=s(h,!0);e(h),k(()=>y(l,t(o).output)),r(v,h)};d(f,v=>{t(C)==="output-available"&&v(x)},g)}};d(A,f=>{t(C)==="input-available"?f(D):f(P,!1)})}r(I,b)},X=(I,b)=>{{var c=A=>{var D=Q(),P=J(D);{var f=x=>{var v=Wt();r(x,v)},g=(x,v)=>{{var h=l=>{var m=Yt(),M=s(m);e(m),k(()=>y(M,`位置: ${t(o).output??""}`)),r(l,m)};d(x,l=>{t(o).state==="output-available"&&l(h)},v)}};d(P,x=>{t(o).state==="input-available"?x(f):x(g,!1)})}r(A,D)},C=(A,D)=>{{var P=f=>{var g=Q(),x=J(g);{var v=l=>{var m=Gt();const M=j(()=>t(o).input);var w=s(m);e(m),k(()=>y(w,`正在获取 ${t(M).city??""} 的天气信息...`)),r(l,m)},h=(l,m)=>{{var M=w=>{var R=Ht();const B=j(()=>t(o).input);var $t=s(R);e(R),k(()=>y($t,`${t(B).city??""} 的天气: ${t(o).output??""}`)),r(w,R)};d(l,w=>{t(o).state==="output-available"&&w(M)},m)}};d(x,l=>{t(o).state==="input-available"?l(v):l(h,!1)})}r(f,g)};d(A,f=>{t(o).type==="tool-getWeatherInformation"&&f(P)},D)}};d(I,A=>{t(o).type==="tool-getLocation"?A(c):A(C,!1)},b)}};d(z,I=>{t(o).type==="tool-askForConfirmation"?I(V):I(X,!1)},N)}};d(Ct,z=>{t(o).type==="text"?z(At):z(St,!1)})}r(O,nt)}),e(E),e($),k(O=>{U($,1,`flex ${t(i).role==="assistant"?"justify-start":"justify-end"}`),U(E,1,`${O??""} max-w-[85%] p-4`),U(_,1,`w-6 h-6 rounded-full ${t(i).role==="assistant"?"bg-blue-500":"bg-gray-500"} flex items-center justify-center text-white text-xs font-bold`),y(L,t(i).role==="assistant"?"AI":"U"),U(F,1,`ml-2 text-xs font-medium ${t(i).role==="assistant"?"text-blue-700":"text-gray-700"}`),y(kt,t(i).role==="assistant"?"AI 助手":"用户")},[()=>xt(t(i).role)]),r(a,$)});var _t=p(ot,2);{var gt=a=>{var i=Vt();r(a,i)};d(_t,a=>{u.status==="loading"&&a(gt)})}e(H);var it=p(H,2),K=s(it),lt=s(K);Ft(lt,{placeholder:"输入您的问题... (支持 Shift+Enter 换行)",class:"min-h-[60px] pr-12 resize-none",onkeydown:a=>{a.key==="Enter"&&!a.shiftKey&&(a.preventDefault(),Y(a))},get value(){return t(S)},set value(a){Z(S,a,!0)}});var yt=p(lt,2);const ht=j(()=>t(et)||!t(S).trim());tt(yt,{"aria-label":"发送消息",get disabled(){return t(ht)},type:"submit",size:"icon",class:"absolute right-2 bottom-2",children:(a,i)=>{Et(a,{})},$$slots:{default:!0}}),e(K);var vt=p(K,2),dt=s(vt),wt=s(dt);e(dt),e(vt),e(it),e(st),e(G),k(()=>y(wt,`状态: ${(u.status==="ready"?"就绪":u.status==="loading"?"处理中":u.status)??""}`)),Dt("submit",K,Y),r(at,G),jt()}zt(["click"]);export{va as component};
