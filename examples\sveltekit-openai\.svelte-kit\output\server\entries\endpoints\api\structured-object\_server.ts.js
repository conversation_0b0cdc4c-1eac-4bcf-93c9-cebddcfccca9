import { l as streamObject } from "../../../../chunks/index.js";
import { n as notificationSchema } from "../../../../chunks/schema.js";
import { c as createOpenAI } from "../../../../chunks/index2.js";
const openai = createOpenAI({
  apiKey: "sk-AA6yOd2Nh9hpBdogB8A2C76bBf7b44E984AcE84973Dd39Cd",
  baseURL: "https://aihubmix.com/v1"
});
async function POST({ request }) {
  const context = await request.json();
  const result = streamObject({
    model: openai("kimi-k2-0711-preview"),
    schema: notificationSchema,
    prompt: `Generate 3 notifications for a messages app in this context:` + context,
    onError: (error) => {
      console.error(error);
    }
  });
  return result.toTextStreamResponse();
}
export {
  POST
};
