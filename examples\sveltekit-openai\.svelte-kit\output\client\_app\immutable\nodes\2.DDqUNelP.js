import{t as a,a as t}from"../chunks/CGanT4Ze.js";import"../chunks/CsOTzF8v.js";var o=a('<main><header class="my-8 text-center"><h1 class="mb-4 text-4xl font-bold">OpenAI + SvelteKit Demo</h1> <p class="text-gray-600">Select a demo to explore different OpenAI integration examples:</p></header> <nav class="flex flex-col gap-4 items-center m-4"><a href="/chat" class="px-6 py-3 w-full max-w-sm text-center bg-gray-100 rounded-lg transition-colors duration-200 hover:bg-gray-200">Chat Demo</a> <a href="/markdown-chat" class="px-6 py-3 w-full max-w-sm text-center bg-blue-100 rounded-lg transition-colors duration-200 hover:bg-blue-200 border-2 border-blue-300">🎨 流式 Markdown 聊天 (新功能)</a> <a href="/streaming-markdown" class="px-6 py-3 w-full max-w-sm text-center bg-green-100 rounded-lg transition-colors duration-200 hover:bg-green-200 border-2 border-green-300">📝 Markdown 动画演示</a> <a href="/completion" class="px-6 py-3 w-full max-w-sm text-center bg-gray-100 rounded-lg transition-colors duration-200 hover:bg-gray-200">Completion Demo</a> <a href="/structured-object" class="px-6 py-3 w-full max-w-sm text-center bg-gray-100 rounded-lg transition-colors duration-200 hover:bg-gray-200">Structured Object Demo</a></nav></main>');function s(e){var r=o();t(e,r)}export{s as component};
