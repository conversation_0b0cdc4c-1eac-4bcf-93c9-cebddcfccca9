import "clsx";
import { h as run } from "./utils.js";
import { s as setContext, k as getContext, m as hasContext } from "./context.js";
const SvelteMap = globalThis.Map;
function createContext(name) {
  const key = Symbol(name);
  return {
    hasContext: () => {
      try {
        return hasContext(key);
      } catch (e) {
        if (typeof e === "object" && e !== null && "message" in e && typeof e.message === "string" && e.message?.includes("lifecycle_outside_component")) {
          return false;
        }
        throw e;
      }
    },
    getContext: () => getContext(key),
    setContext: (value) => setContext(key, value)
  };
}
class KeyedStore extends SvelteMap {
  #itemConstructor;
  constructor(itemConstructor, value) {
    super(value);
    this.#itemConstructor = itemConstructor;
  }
  get(key) {
    const test = super.get(key) ?? // Untrack here because this is technically a state mutation, meaning
    // deriveds downstream would fail. Because this is idempotent (even
    // though it's not pure), it's safe.
    run(() => this.set(key, new this.#itemConstructor())).get(key);
    return test;
  }
}
export {
  KeyedStore as K,
  SvelteMap as S,
  createContext as c
};
