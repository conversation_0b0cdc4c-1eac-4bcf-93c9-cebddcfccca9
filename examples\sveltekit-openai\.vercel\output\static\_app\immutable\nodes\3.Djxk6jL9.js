import{t as h,a as o,c as P,b as H}from"../chunks/CGanT4Ze.js";import{p as ma,t as x,a as pa,c as u,r,s as R,b as aa,d as _a,g as a,u as L,f as A,n as ta}from"../chunks/Cc9lkQ6R.js";import{e as ga,s as w}from"../chunks/2Hh1nz_V.js";import{i as n}from"../chunks/0U5e1eCd.js";import{e as ea,A as xa,i as ha}from"../chunks/zbNWLoGC.js";import{s as ya}from"../chunks/krzcCheT.js";import{B as Q}from"../chunks/BrOQj_DL.js";import{T as ba}from"../chunks/CEXehBSS.js";import{S as wa}from"../chunks/BjpmZ2ke.js";import{C as Ca}from"../chunks/U6ZYfsmc.js";var ka=h('<div class="flex flex-col gap-2"> <div class="flex gap-2"><!> <!></div></div>'),Ia=h('<div class="text-gray-500"> </div>'),Ta=h('<div class="text-gray-500">Getting location...</div>'),Sa=h('<div class="text-gray-500"> </div>'),La=h("<pre> </pre>"),Na=h('<div class="text-gray-500"> </div>'),Da=h('<div class="text-gray-500"> </div>'),Fa=h("<div></div>"),Ma=h('<main class="flex flex-col items-center h-dvh w-dvw"><div class="grid h-full w-full max-w-4xl grid-cols-1 grid-rows-[1fr,120px] p-2"><div class="overflow-y-auto w-full h-full"></div> <form class="relative"><p> </p> <div><a href="/chat/1">chat 1</a> <a href="/chat/2">chat 2</a> <a href="/chat/3">chat 3</a></div> <!> <!></form></div></main>');function Ea(oa,sa){ma(sa,!0);const C=new Ca({async onToolCall({toolCall:e}){if(await new Promise(_=>setTimeout(_,2e3)),e.toolName==="getLocation"){const _=["New York","Los Angeles","Chicago","San Francisco"],I=_[Math.floor(Math.random()*_.length)];await C.addToolResult({toolCallId:e.toolCallId,tool:"getLocation",output:I})}}});let Y=_a("");const ra=L(()=>C.status!=="ready");function ia(e){return e==="assistant"?"bg-primary text-secondary rounded-md":"bg-secondary text-primary rounded-md justify-self-end"}function U(e){e.preventDefault(),C.sendMessage({text:a(Y)}),aa(Y,"")}var G=Ma(),V=u(G),W=u(V);ea(W,21,()=>C.messages,e=>e.id,(e,_)=>{var I=Fa();ea(I,21,()=>a(_).parts,ha,(E,t)=>{var Z=P(),na=A(Z);{var da=T=>{var $=P(),J=A($);{var K=d=>{wa(d,{get content(){return a(t).text},animationDelay:20,fadeInDuration:400})},y=d=>{var k=H();x(()=>w(k,a(t).text)),o(d,k)};n(J,d=>{a(_).role==="assistant"?d(K):d(y,!1)})}o(T,$)},fa=(T,$)=>{{var J=y=>{var d=P();const k=L(()=>a(t).toolCallId),B=L(()=>a(t).state);var b=A(d);{var S=i=>{var c=ka();const l=L(()=>a(t).input);var v=u(c),m=R(v),s=u(m);Q(s,{variant:"default",onclick:()=>C.addToolResult({toolCallId:a(k),tool:"askForConfirmation",output:"Yes, confirmed"}),children:(g,D)=>{ta();var p=H("Yes");o(g,p)},$$slots:{default:!0}});var f=R(s,2);Q(f,{variant:"secondary",onclick:()=>C.addToolResult({toolCallId:a(k),tool:"askForConfirmation",output:"No, denied"}),children:(g,D)=>{ta();var p=H("No");o(g,p)},$$slots:{default:!0}}),r(m),r(c),x(()=>w(v,`${a(l).message??""} `)),o(i,c)},N=(i,c)=>{{var l=v=>{var m=Ia(),s=u(m,!0);r(m),x(()=>w(s,a(t).output)),o(v,m)};n(i,v=>{a(B)==="output-available"&&v(l)},c)}};n(b,i=>{a(B)==="input-available"?i(S):i(N,!1)})}o(y,d)},K=(y,d)=>{{var k=b=>{var S=P(),N=A(S);{var i=l=>{var v=Ta();o(l,v)},c=(l,v)=>{{var m=s=>{var f=Sa(),g=u(f);r(f),x(()=>w(g,`Location: ${a(t).output??""}`)),o(s,f)};n(l,s=>{a(t).state==="output-available"&&s(m)},v)}};n(N,l=>{a(t).state==="input-available"?l(i):l(c,!1)})}o(b,S)},B=(b,S)=>{{var N=i=>{var c=P(),l=A(c);{var v=s=>{var f=La(),g=u(f,!0);r(f),x(D=>w(g,D),[()=>JSON.stringify(a(t),null,2)]),o(s,f)},m=(s,f)=>{{var g=p=>{var F=Na();const O=L(()=>a(t).input);var M=u(F);r(F),x(()=>w(M,`Getting weather information for ${a(O).city??""}...`)),o(p,F)},D=(p,F)=>{{var O=M=>{var q=Da();const ua=L(()=>a(t).input);var ca=u(q);r(q),x(()=>w(ca,`Weather in ${a(ua).city??""}: ${a(t).output??""}`)),o(M,q)};n(p,M=>{a(t).state==="output-available"&&M(O)},F)}};n(s,p=>{a(t).state==="input-available"?p(g):p(D,!1)},f)}};n(l,s=>{a(t).state==="input-streaming"?s(v):s(m,!1)})}o(i,c)};n(b,i=>{a(t).type==="tool-getWeatherInformation"&&i(N)},S)}};n(y,b=>{a(t).type==="tool-getLocation"?b(k):b(B,!1)},d)}};n(T,y=>{a(t).type==="tool-askForConfirmation"?y(J):y(K,!1)},$)}};n(na,T=>{a(t).type==="text"?T(da):T(fa,!1)})}o(E,Z)}),r(I),x(E=>ya(I,1,`${E??""} my-2 max-w-[80%] p-2 flex flex-col gap-2`),[()=>ia(a(_).role)]),o(e,I)}),r(W);var j=R(W,2),z=u(j),la=u(z,!0);r(z);var X=R(z,4);ba(X,{placeholder:"Send a message...",class:"h-full",onkeydown:e=>{e.key==="Enter"&&!e.shiftKey&&(e.preventDefault(),U(e))},get value(){return a(Y)},set value(e){aa(Y,e,!0)}});var va=R(X,2);Q(va,{"aria-label":"Send message",get disabled(){return a(ra)},type:"submit",size:"icon",class:"absolute right-3 bottom-3",children:(e,_)=>{xa(e,{})},$$slots:{default:!0}}),r(j),r(V),r(G),x(()=>w(la,C.status)),ga("submit",j,U),o(oa,G),pa()}export{Ea as component};
