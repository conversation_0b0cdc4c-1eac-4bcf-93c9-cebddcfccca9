import{t as Ae,a as we}from"./CGanT4Ze.js";import{o as Ne}from"./BwxiO7wK.js";import{p as Ce,j as Te,a as Ie,g as f,d as Le,b as De}from"./Cc9lkQ6R.js";import{p as W,b as Re}from"./0U5e1eCd.js";const H=1,F=2,V=3,Z=4,p=5,ee=6,ne=7,te=8,U=9,v=10,Q=11,y=12,N=13,C=14,T=15,I=16,x=17,X=18,u=19,L=20,b=21,ge=22,D=23,G=24,A=25,fe=26,j=27,B=28,S=29,k=30,g=31,O=101,oe=102,ke=103,le=104,P=105,z=1,_e=2,he=4,be=8,me=16;function Oe(e){switch(e){case z:return"href";case _e:return"src";case he:return"class";case be:return"checked";case me:return"start"}}const qe=e=>{switch(e){case 1:return V;case 2:return Z;case 3:return p;case 4:return ee;case 5:return ne;default:return te}},ye=qe,ue=24;function Be(e){const a=new Uint32Array(ue);return a[0]=H,{renderer:e,text:"",pending:"",tokens:a,len:0,token:H,fence_end:0,blockquote_idx:0,hr_char:"",hr_chars:0,fence_start:0,spaces:new Uint8Array(ue),indent:"",indent_len:0,table_state:0}}function c(e){e.text.length!==0&&(console.assert(e.len>0,"Never adding text to root"),e.renderer.add_text(e.renderer.data,e.text),e.text="")}function d(e){console.assert(e.len>0,"No nodes to end"),e.len-=1,e.token=e.tokens[e.len],e.renderer.end_token(e.renderer.data)}function s(e,a){(e.tokens[e.len]===G||e.tokens[e.len]===D)&&a!==A&&d(e),e.len+=1,e.tokens[e.len]=a,e.token=a,e.renderer.add_token(e.renderer.data,a)}function Se(e,a,n){for(;n<=e.len;){if(e.tokens[n]===a)return n;n+=1}return-1}function m(e,a){for(e.fence_start=0;e.len>a;)d(e)}function q(e,a){let n=0;for(let t=0;t<=e.len&&(a-=e.spaces[t],!(a<0));t+=1)switch(e.tokens[t]){case U:case v:case L:case A:n=t;break}for(;e.len>n;)d(e);return a}function Y(e,a){let n=-1,t=-1;for(let r=e.blockquote_idx+1;r<=e.len;r+=1)if(e.tokens[r]===A){if(e.indent_len<e.spaces[r]){t=-1;break}t=r}else e.tokens[r]===a&&(n=r);return t===-1?n===-1?(m(e,e.blockquote_idx),s(e,a),!0):(m(e,n),!1):(m(e,t),s(e,a),!0)}function $(e,a){s(e,A),e.spaces[e.len]=e.indent_len+a,_(e),e.token=ke}function _(e){e.indent="",e.indent_len=0,e.pending=""}function J(e){switch(e){case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return!0;default:return!1}}function He(e){switch(e){case 32:case 58:case 59:case 41:case 44:case 33:case 46:case 63:case 93:case 10:return!0;default:return!1}}function Ue(e){return J(e)||He(e)}function l(e,a){for(const n of a){if(e.token===O){switch(n){case" ":e.indent_len+=1;continue;case"	":e.indent_len+=4;continue}let r=q(e,e.indent_len);e.indent_len=0,e.token=e.tokens[e.len],r>0&&l(e," ".repeat(r))}const t=e.pending+n;switch(e.token){case b:case H:case L:case G:case D:switch(console.assert(e.text.length===0,"Root should not have any text"),e.pending[0]){case void 0:e.pending=n;continue;case" ":console.assert(e.pending.length===1),e.pending=n,e.indent+=" ",e.indent_len+=1;continue;case"	":console.assert(e.pending.length===1),e.pending=n,e.indent+="	",e.indent_len+=4;continue;case`
`:if(console.assert(e.pending.length===1),e.tokens[e.len]===A&&e.token===b){d(e),_(e),e.pending=n;continue}m(e,e.blockquote_idx),_(e),e.blockquote_idx=0,e.fence_start=0,e.pending=n;continue;case"#":switch(n){case"#":if(e.pending.length<6){e.pending=t;continue}break;case" ":q(e,e.indent_len),s(e,ye(e.pending.length)),_(e);continue}break;case">":{const i=Se(e,L,e.blockquote_idx+1);i===-1?(m(e,e.blockquote_idx),e.blockquote_idx+=1,e.fence_start=0,s(e,L)):e.blockquote_idx=i,_(e),e.pending=n;continue}case"-":case"*":case"_":if(e.hr_chars===0&&(console.assert(e.pending.length===1,"Pending should be one character"),e.hr_chars=1,e.hr_char=e.pending),e.hr_chars>0){switch(n){case e.hr_char:e.hr_chars+=1,e.pending=t;continue;case" ":e.pending=t;continue;case`
`:if(e.hr_chars<3)break;q(e,e.indent_len),e.renderer.add_token(e.renderer.data,ge),e.renderer.end_token(e.renderer.data),_(e),e.hr_chars=0;continue}e.hr_chars=0}if(e.pending[0]!=="_"&&e.pending[1]===" "){Y(e,D),$(e,2),l(e,t.slice(2));continue}break;case"`":if(e.pending.length<3){if(n==="`"){e.pending=t,e.fence_start=t.length;continue}e.fence_start=0;break}switch(n){case"`":e.pending.length===e.fence_start?(e.pending=t,e.fence_start=t.length):(s(e,F),_(e),e.fence_start=0,l(e,t));continue;case`
`:{q(e,e.indent_len),s(e,v),e.pending.length>e.fence_start&&e.renderer.set_attr(e.renderer.data,he,e.pending.slice(e.fence_start)),_(e),e.token=O;continue}default:e.pending=t;continue}case"+":if(n!==" ")break;Y(e,D),$(e,2);continue;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":if(e.pending[e.pending.length-1]==="."){if(n!==" ")break;Y(e,G)&&e.pending!=="1."&&e.renderer.set_attr(e.renderer.data,me,e.pending.slice(0,-1)),$(e,e.pending.length+1);continue}else{const i=n.charCodeAt(0);if(i===46||J(i)){e.pending=t;continue}}break;case"|":m(e,e.blockquote_idx),s(e,j),s(e,B),e.pending="",l(e,n);continue}let r=t;if(e.token===b)e.token=e.tokens[e.len],e.renderer.add_token(e.renderer.data,b),e.renderer.end_token(e.renderer.data);else if(e.indent_len>=4){let i=0;for(;i<4;i+=1)if(e.indent[i]==="	"){i=i+1;break}r=e.indent.slice(i)+t,s(e,U)}else s(e,F);_(e),l(e,r);continue;case j:if(e.table_state===1)switch(n){case"-":case" ":case"|":case":":e.pending=t;continue;case`
`:e.table_state=2,e.pending="";continue;default:d(e),e.table_state=0;break}else switch(e.pending){case"|":s(e,B),e.pending="",l(e,n);continue;case`
`:d(e),e.pending="",e.table_state=0,l(e,n);continue}break;case B:switch(e.pending){case"":break;case"|":s(e,S),d(e),e.pending="",l(e,n);continue;case`
`:d(e),e.table_state=Math.min(e.table_state+1,2),e.pending="",l(e,n);continue;default:s(e,S),l(e,n);continue}break;case S:if(e.pending==="|"){c(e),d(e),e.pending="",l(e,n);continue}break;case U:switch(t){case`
    `:case`
   	`:case`
  	`:case`
 	`:case`
	`:e.text+=`
`,e.pending="";continue;case`
`:case`
 `:case`
  `:case`
   `:e.pending=t;continue;default:e.pending.length!==0?(c(e),d(e),e.pending=n):e.text+=n;continue}case v:switch(n){case"`":e.pending=t;continue;case`
`:if(t.length===e.fence_start+e.fence_end+1){c(e),d(e),e.pending="",e.fence_start=0,e.fence_end=0,e.token=O;continue}e.token=O;break;case" ":if(e.pending[0]===`
`){e.pending=t,e.fence_end+=1;continue}break}e.text+=e.pending,e.pending=n,e.fence_end=1;continue;case Q:switch(n){case"`":t.length===e.fence_start+ +(e.pending[0]===" ")?(c(e),d(e),e.pending="",e.fence_start=0):e.pending=t;continue;case`
`:e.text+=e.pending,e.pending="",e.token=b,e.blockquote_idx=0,c(e);continue;case" ":e.text+=e.pending,e.pending=n;continue;default:e.text+=t,e.pending="";continue}case ke:switch(e.pending.length){case 0:if(n!=="[")break;e.pending=t;continue;case 1:if(n!==" "&&n!=="x")break;e.pending=t;continue;case 2:if(n!=="]")break;e.pending=t;continue;case 3:if(n!==" ")break;e.renderer.add_token(e.renderer.data,fe),e.pending[1]==="x"&&e.renderer.set_attr(e.renderer.data,be,""),e.renderer.end_token(e.renderer.data),e.pending=" ";continue}e.token=e.tokens[e.len],e.pending="",l(e,t);continue;case C:case T:{let i="*",o=y;if(e.token===T&&(i="_",o=N),i===e.pending){if(c(e),i===n){d(e),e.pending="";continue}s(e,o),e.pending=n;continue}break}case y:case N:{let i="*",o=C;switch(e.token===N&&(i="_",o=T),e.pending){case i:i===n?e.tokens[e.len-1]===o?e.pending=t:(c(e),s(e,o),e.pending=""):(c(e),d(e),e.pending=n);continue;case i+i:const E=e.token;c(e),d(e),d(e),i!==n?(s(e,E),e.pending=n):e.pending="";continue}break}case I:if(t==="~~"){c(e),d(e),e.pending="";continue}break;case P:n===`
`?(c(e),s(e,k),e.pending=""):(e.token=e.tokens[e.len],e.pending[0]==="\\"?e.text+="[":e.text+="$$",e.pending="",l(e,n));continue;case k:if(t==="\\]"||t==="$$"){c(e),d(e),e.pending="";continue}break;case g:if(t==="\\)"||e.pending[0]==="$"){c(e),d(e),n===")"?e.pending="":e.pending=n;continue}break;case oe:t==="http://"||t==="https://"?(c(e),s(e,X),e.pending=t,e.text=t):"http:/"[e.pending.length]===n||"https:/"[e.pending.length]===n?e.pending=t:(e.token=e.tokens[e.len],l(e,n));continue;case x:case u:if(e.pending==="]"){c(e),n==="("?e.pending=t:(d(e),e.pending=n);continue}if(e.pending[0]==="]"&&e.pending[1]==="("){if(n===")"){const i=e.token===x?z:_e,o=e.pending.slice(2);e.renderer.set_attr(e.renderer.data,i,o),d(e),e.pending=""}else e.pending+=n;continue}break;case X:n===" "||n===`
`||n==="\\"?(e.renderer.set_attr(e.renderer.data,z,e.pending),c(e),d(e),e.pending=n):(e.text+=n,e.pending=t);continue;case le:if(t.startsWith("<br")){if(t.length===3||n===" "||n==="/"&&(t.length===4||e.pending[e.pending.length-1]===" ")){e.pending=t;continue}if(n===">"){c(e),e.token=e.tokens[e.len],e.renderer.add_token(e.renderer.data,b),e.renderer.end_token(e.renderer.data),e.pending="";continue}}e.token=e.tokens[e.len],e.text+="<",e.pending=e.pending.slice(1),l(e,n);continue}switch(e.pending[0]){case"\\":if(e.token===u||e.token===k||e.token===g)break;switch(n){case"(":c(e),s(e,g),e.pending="";continue;case"[":e.token=P,e.pending=t;continue;case`
`:e.pending=n;continue;default:let r=n.charCodeAt(0);e.pending="",e.text+=J(r)||r>=65&&r<=90||r>=97&&r<=122?t:n;continue}case`
`:switch(e.token){case u:case k:case g:break;case V:case Z:case p:case ee:case ne:case te:c(e),m(e,e.blockquote_idx),e.blockquote_idx=0,e.pending=n;continue;default:c(e),e.pending=n,e.token=b,e.blockquote_idx=0;continue}break;case"<":if(e.token!==u&&e.token!==k&&e.token!==g){c(e),e.pending=t,e.token=le;continue}break;case"`":if(e.token===u)break;n==="`"?(e.fence_start+=1,e.pending=t):(e.fence_start+=1,c(e),s(e,Q),e.text=n===" "||n===`
`?"":n,e.pending="");continue;case"_":case"*":{if(e.token===u||e.token===k||e.token===g||e.token===C)break;let r=y,i=C;const o=e.pending[0];if(o==="_"&&(r=N,i=T),e.pending.length===1){if(o===n){e.pending=t;continue}if(n!==" "&&n!==`
`){c(e),s(e,r),e.pending=n;continue}}else{if(o===n){c(e),s(e,i),s(e,r),e.pending="";continue}if(n!==" "&&n!==`
`){c(e),s(e,i),e.pending=n;continue}}break}case"~":if(e.token!==u&&e.token!==I){if(e.pending==="~"){if(n==="~"){e.pending=t;continue}}else if(n!==" "&&n!==`
`){c(e),s(e,I),e.pending=n;continue}}break;case"$":if(e.token!==u&&e.token!==I&&e.pending==="$")if(n==="$"){e.token=P,e.pending=t;continue}else{if(Ue(n.charCodeAt(0)))break;c(e),s(e,g),e.pending=n;continue}break;case"[":if(e.token!==u&&e.token!==x&&e.token!==k&&e.token!==g&&n!=="]"){c(e),s(e,x),e.pending=n;continue}break;case"!":if(e.token!==u&&n==="["){c(e),s(e,u),e.pending="";continue}break;case" ":if(e.pending.length===1&&n===" ")continue;break}if(e.token!==u&&e.token!==x&&e.token!==k&&e.token!==g&&n==="h"&&(e.pending===" "||e.pending==="")){e.text+=e.pending,e.pending=n,e.token=oe;continue}e.text+=e.pending,e.pending=n}c(e)}function ve(e){return{add_token:Ge,end_token:Ke,add_text:Me,set_attr:We,data:{nodes:[e,,,,,],index:0}}}function Ge(e,a){var r;let n=e.nodes[e.index],t;switch(a){case H:return;case L:t=document.createElement("blockquote");break;case F:t=document.createElement("p");break;case b:t=document.createElement("br");break;case ge:t=document.createElement("hr");break;case V:t=document.createElement("h1");break;case Z:t=document.createElement("h2");break;case p:t=document.createElement("h3");break;case ee:t=document.createElement("h4");break;case ne:t=document.createElement("h5");break;case te:t=document.createElement("h6");break;case y:case N:t=document.createElement("em");break;case C:case T:t=document.createElement("strong");break;case I:t=document.createElement("s");break;case Q:t=document.createElement("code");break;case X:case x:t=document.createElement("a");break;case u:t=document.createElement("img");break;case D:t=document.createElement("ul");break;case G:t=document.createElement("ol");break;case A:t=document.createElement("li");break;case fe:let i=t=document.createElement("input");i.type="checkbox",i.disabled=!0;break;case U:case v:n=n.appendChild(document.createElement("pre")),t=document.createElement("code");break;case j:t=document.createElement("table");break;case B:switch(n.children.length){case 0:n=n.appendChild(document.createElement("thead"));break;case 1:n=n.appendChild(document.createElement("tbody"));break;default:n=n.children[1]}t=document.createElement("tr");break;case S:t=document.createElement(((r=n.parentElement)==null?void 0:r.tagName)==="THEAD"?"th":"td");break;case k:t=document.createElement("equation-block");break;case g:t=document.createElement("equation-inline");break}e.nodes[++e.index]=n.appendChild(t)}function Ke(e){e.index-=1}function Me(e,a){e.nodes[e.index].appendChild(document.createTextNode(a))}function We(e,a,n){e.nodes[e.index].setAttribute(Oe(a),n)}var Pe=Ae('<div class="streaming-markdown"></div>');function Xe(e,a){Ce(a,!0);let n=W(a,"content",3,""),t=W(a,"animationDelay",3,20),r=W(a,"fadeInDuration",3,400),i=Le(void 0),o,E="",K=0;function ie(){if(!f(i))return;const h=document.createTreeWalker(f(i),NodeFilter.SHOW_TEXT,null);let se;for(;se=h.nextNode();){const M=se,R=M.parentElement;if(R&&!R.classList.contains("animated")){R.classList.add("animated");const xe=M.textContent||"",re=document.createDocumentFragment();xe.split("").forEach(de=>{const w=document.createElement("span");w.className="fade-in-char",w.textContent=de===" "?" ":de,w.style.animationDelay=`${K*t()}ms`,w.style.animationDuration=`${r()}ms`,re.appendChild(w),K++}),R.replaceChild(re,M)}}}function Ee(){if(!o||!f(i))return;const h=n().slice(E.length);h&&(l(o,h),E=n(),setTimeout(()=>ie(),0))}function ae(){if(!f(i))return;f(i).innerHTML="",K=0;const h=ve(f(i));o=Be(h),n()&&(l(o,n()),E=n(),setTimeout(()=>ie(),0))}Te(()=>{f(i)&&(n().length<E.length?ae():Ee())}),Ne(()=>{f(i)&&ae()});var ce=Pe();Re(ce,h=>De(i,h),()=>f(i)),we(e,ce),Ie()}export{Xe as S};
