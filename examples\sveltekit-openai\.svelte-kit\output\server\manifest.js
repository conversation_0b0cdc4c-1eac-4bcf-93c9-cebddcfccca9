export const manifest = (() => {
function __memo(fn) {
	let value;
	return () => value ??= (value = fn());
}

return {
	appDir: "_app",
	appPath: "_app",
	assets: new Set(["favicon.png","robots.txt"]),
	mimeTypes: {".png":"image/png",".txt":"text/plain"},
	_: {
		client: {start:"_app/immutable/entry/start.DfY3aO-v.js",app:"_app/immutable/entry/app.D7InXtR4.js",imports:["_app/immutable/entry/start.DfY3aO-v.js","_app/immutable/chunks/CaKqO4GF.js","_app/immutable/chunks/Cc9lkQ6R.js","_app/immutable/chunks/BwxiO7wK.js","_app/immutable/entry/app.D7InXtR4.js","_app/immutable/chunks/Cc9lkQ6R.js","_app/immutable/chunks/2Hh1nz_V.js","_app/immutable/chunks/CGanT4Ze.js","_app/immutable/chunks/BwxiO7wK.js","_app/immutable/chunks/0U5e1eCd.js"],stylesheets:[],fonts:[],uses_env_dynamic_public:false},
		nodes: [
			__memo(() => import('./nodes/0.js')),
			__memo(() => import('./nodes/1.js')),
			__memo(() => import('./nodes/2.js')),
			__memo(() => import('./nodes/3.js')),
			__memo(() => import('./nodes/4.js')),
			__memo(() => import('./nodes/5.js')),
			__memo(() => import('./nodes/6.js')),
			__memo(() => import('./nodes/7.js')),
			__memo(() => import('./nodes/8.js'))
		],
		routes: [
			{
				id: "/",
				pattern: /^\/$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 2 },
				endpoint: null
			},
			{
				id: "/api/chat",
				pattern: /^\/api\/chat\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/chat/_server.ts.js'))
			},
			{
				id: "/api/completion",
				pattern: /^\/api\/completion\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/completion/_server.ts.js'))
			},
			{
				id: "/api/structured-object",
				pattern: /^\/api\/structured-object\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/structured-object/_server.ts.js'))
			},
			{
				id: "/chat",
				pattern: /^\/chat\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 3 },
				endpoint: null
			},
			{
				id: "/chat/[id]",
				pattern: /^\/chat\/([^/]+?)\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,], errors: [1,], leaf: 4 },
				endpoint: null
			},
			{
				id: "/completion",
				pattern: /^\/completion\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 5 },
				endpoint: null
			},
			{
				id: "/markdown-chat",
				pattern: /^\/markdown-chat\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 6 },
				endpoint: null
			},
			{
				id: "/streaming-markdown",
				pattern: /^\/streaming-markdown\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 7 },
				endpoint: null
			},
			{
				id: "/structured-object",
				pattern: /^\/structured-object\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 8 },
				endpoint: null
			}
		],
		prerendered_routes: new Set([]),
		matchers: async () => {
			
			return {  };
		},
		server_assets: {}
	}
}
})();
