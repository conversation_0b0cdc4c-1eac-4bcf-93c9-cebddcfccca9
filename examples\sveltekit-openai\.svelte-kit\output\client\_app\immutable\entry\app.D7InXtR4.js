const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["../nodes/0.B-R9LGcN.js","../chunks/CGanT4Ze.js","../chunks/Cc9lkQ6R.js","../chunks/Decf1TKS.js","../chunks/vHm_wtdr.js","../chunks/DplMobLG.js","../chunks/Csn8UOt9.js","../assets/0.scEZELUK.css","../nodes/1.isyMjBx1.js","../chunks/CsOTzF8v.js","../chunks/2Hh1nz_V.js","../chunks/CRU_BPGW.js","../chunks/Bq-mu1H_.js","../chunks/CaKqO4GF.js","../chunks/BwxiO7wK.js","../nodes/2.DDqUNelP.js","../nodes/3.Djxk6jL9.js","../chunks/0U5e1eCd.js","../chunks/zbNWLoGC.js","../chunks/krzcCheT.js","../chunks/BrOQj_DL.js","../chunks/CEXehBSS.js","../chunks/BjpmZ2ke.js","../assets/StreamingMarkdown.BsuaRF1j.css","../chunks/U6ZYfsmc.js","../nodes/4.CBi5-YtA.js","../nodes/5.ayhg-geQ.js","../nodes/6.B65Piw58.js","../assets/6.CWupZ8fR.css","../nodes/7.BcNOHhdZ.js","../assets/7.CHTwDonU.css","../nodes/8.D9pe05h7.js"])))=>i.map(i=>d[i]);
var W=r=>{throw TypeError(r)};var z=(r,t,s)=>t.has(r)||W("Cannot "+s);var u=(r,t,s)=>(z(r,t,"read from private field"),s?s.call(r):t.get(r)),x=(r,t,s)=>t.has(r)?W("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(r):t.set(r,s),D=(r,t,s,n)=>(z(r,t,"write to private field"),n?n.call(r,s):t.set(r,s),s);import{C as G,O as Q,y as X,E as Z,z as M,W as $,D as tt,b as T,an as et,g,aE as rt,ay as st,F as at,p as nt,i as ot,j as it,d as S,aF as ct,f as L,s as ut,a as lt,c as mt,r as dt,u as C,t as ft}from"../chunks/Cc9lkQ6R.js";import{h as _t,m as ht,u as vt,s as gt}from"../chunks/2Hh1nz_V.js";import{t as Y,a as O,c as I,b as Et}from"../chunks/CGanT4Ze.js";import{o as yt}from"../chunks/BwxiO7wK.js";import{p as V,i as j,b as F}from"../chunks/0U5e1eCd.js";function B(r,t,s){G&&Q();var n=r,o,c;X(()=>{o!==(o=t())&&(c&&($(c),c=null),o&&(c=M(()=>s(n,o))))},Z),G&&(n=tt)}function bt(r){return class extends Pt{constructor(t){super({component:r,...t})}}}var E,m;class Pt{constructor(t){x(this,E);x(this,m);var c;var s=new Map,n=(a,e)=>{var d=at(e);return s.set(a,d),d};const o=new Proxy({...t.props||{},$$events:{}},{get(a,e){return g(s.get(e)??n(e,Reflect.get(a,e)))},has(a,e){return e===et?!0:(g(s.get(e)??n(e,Reflect.get(a,e))),Reflect.has(a,e))},set(a,e,d){return T(s.get(e)??n(e,d),d),Reflect.set(a,e,d)}});D(this,m,(t.hydrate?_t:ht)(t.component,{target:t.target,anchor:t.anchor,props:o,context:t.context,intro:t.intro??!1,recover:t.recover})),(!((c=t==null?void 0:t.props)!=null&&c.$$host)||t.sync===!1)&&rt(),D(this,E,o.$$events);for(const a of Object.keys(u(this,m)))a==="$set"||a==="$destroy"||a==="$on"||st(this,a,{get(){return u(this,m)[a]},set(e){u(this,m)[a]=e},enumerable:!0});u(this,m).$set=a=>{Object.assign(o,a)},u(this,m).$destroy=()=>{vt(u(this,m))}}$set(t){u(this,m).$set(t)}$on(t,s){u(this,E)[t]=u(this,E)[t]||[];const n=(...o)=>s.call(this,...o);return u(this,E)[t].push(n),()=>{u(this,E)[t]=u(this,E)[t].filter(o=>o!==n)}}$destroy(){u(this,m).$destroy()}}E=new WeakMap,m=new WeakMap;const Rt="modulepreload",Ot=function(r,t){return new URL(r,t).href},N={},v=function(t,s,n){let o=Promise.resolve();if(s&&s.length>0){const a=document.getElementsByTagName("link"),e=document.querySelector("meta[property=csp-nonce]"),d=(e==null?void 0:e.nonce)||(e==null?void 0:e.getAttribute("nonce"));o=Promise.allSettled(s.map(l=>{if(l=Ot(l,n),l in N)return;N[l]=!0;const y=l.endsWith(".css"),p=y?'[rel="stylesheet"]':"";if(!!n)for(let b=a.length-1;b>=0;b--){const i=a[b];if(i.href===l&&(!y||i.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${l}"]${p}`))return;const _=document.createElement("link");if(_.rel=y?"stylesheet":Rt,y||(_.as="script"),_.crossOrigin="",_.href=l,d&&_.setAttribute("nonce",d),document.head.appendChild(_),y)return new Promise((b,i)=>{_.addEventListener("load",b),_.addEventListener("error",()=>i(new Error(`Unable to preload CSS for ${l}`)))})}))}function c(a){const e=new Event("vite:preloadError",{cancelable:!0});if(e.payload=a,window.dispatchEvent(e),!e.defaultPrevented)throw a}return o.then(a=>{for(const e of a||[])e.status==="rejected"&&c(e.reason);return t().catch(c)})},jt={};var wt=Y('<div id="svelte-announcer" aria-live="assertive" aria-atomic="true" style="position: absolute; left: 0; top: 0; clip: rect(0 0 0 0); clip-path: inset(50%); overflow: hidden; white-space: nowrap; width: 1px; height: 1px"><!></div>'),kt=Y("<!> <!>",1);function At(r,t){nt(t,!0);let s=V(t,"components",23,()=>[]),n=V(t,"data_0",3,null),o=V(t,"data_1",3,null);ot(()=>t.stores.page.set(t.page)),it(()=>{t.stores,t.page,t.constructors,s(),t.form,n(),o(),t.stores.page.notify()});let c=S(!1),a=S(!1),e=S(null);yt(()=>{const i=t.stores.page.subscribe(()=>{g(c)&&(T(a,!0),ct().then(()=>{T(e,document.title||"untitled page",!0)}))});return T(c,!0),i});const d=C(()=>t.constructors[1]);var l=kt(),y=L(l);{var p=i=>{var h=I();const w=C(()=>t.constructors[0]);var k=L(h);B(k,()=>g(w),(P,R)=>{F(R(P,{get data(){return n()},get form(){return t.form},children:(f,pt)=>{var U=I(),H=L(U);B(H,()=>g(d),(J,K)=>{F(K(J,{get data(){return o()},get form(){return t.form}}),A=>s()[1]=A,()=>{var A;return(A=s())==null?void 0:A[1]})}),O(f,U)},$$slots:{default:!0}}),f=>s()[0]=f,()=>{var f;return(f=s())==null?void 0:f[0]})}),O(i,h)},q=i=>{var h=I();const w=C(()=>t.constructors[0]);var k=L(h);B(k,()=>g(w),(P,R)=>{F(R(P,{get data(){return n()},get form(){return t.form}}),f=>s()[0]=f,()=>{var f;return(f=s())==null?void 0:f[0]})}),O(i,h)};j(y,i=>{t.constructors[1]?i(p):i(q,!1)})}var _=ut(y,2);{var b=i=>{var h=wt(),w=mt(h);{var k=P=>{var R=Et();ft(()=>gt(R,g(e))),O(P,R)};j(w,P=>{g(a)&&P(k)})}dt(h),O(i,h)};j(_,i=>{g(c)&&i(b)})}O(r,l),lt()}const Ft=bt(At),Bt=[()=>v(()=>import("../nodes/0.B-R9LGcN.js"),__vite__mapDeps([0,1,2,3,4,5,6,7]),import.meta.url),()=>v(()=>import("../nodes/1.isyMjBx1.js"),__vite__mapDeps([8,1,2,9,10,11,12,13,14]),import.meta.url),()=>v(()=>import("../nodes/2.DDqUNelP.js"),__vite__mapDeps([15,1,2,9]),import.meta.url),()=>v(()=>import("../nodes/3.Djxk6jL9.js"),__vite__mapDeps([16,1,2,10,17,18,19,20,3,21,22,14,23,24]),import.meta.url),()=>v(()=>import("../nodes/4.CBi5-YtA.js"),__vite__mapDeps([25,1,2,10,17,18,19,12,13,14,20,3,21,24]),import.meta.url),()=>v(()=>import("../nodes/5.ayhg-geQ.js"),__vite__mapDeps([26,1,2,9,10,17,11,21,19,4,5]),import.meta.url),()=>v(()=>import("../nodes/6.B65Piw58.js"),__vite__mapDeps([27,1,2,10,17,18,19,20,3,21,22,14,23,24,28]),import.meta.url),()=>v(()=>import("../nodes/7.BcNOHhdZ.js"),__vite__mapDeps([29,1,2,10,22,14,17,23,20,3,19,30]),import.meta.url),()=>v(()=>import("../nodes/8.D9pe05h7.js"),__vite__mapDeps([31,1,2,10,17,18,19,20,3,21,6,5]),import.meta.url)],qt=[],Ut={"/":[2],"/chat":[3],"/chat/[id]":[4],"/completion":[5],"/markdown-chat":[6],"/streaming-markdown":[7],"/structured-object":[8]},Lt={handleError:({error:r})=>{console.error(r)},reroute:()=>{},transport:{}},Tt=Object.fromEntries(Object.entries(Lt.transport).map(([r,t])=>[r,t.decode])),Wt=!1,zt=(r,t)=>Tt[r](t);export{zt as decode,Tt as decoders,Ut as dictionary,Wt as hash,Lt as hooks,jt as matchers,Bt as nodes,Ft as root,qt as server_loads};
