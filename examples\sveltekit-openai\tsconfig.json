{"extends": "./.svelte-kit/tsconfig.json", "compilerOptions": {"allowJs": true, "checkJs": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "skipLibCheck": true, "sourceMap": true, "strict": true, "module": "NodeNext", "moduleResolution": "NodeNext", "declaration": true, "declarationMap": true, "stripInternal": true}, "references": [{"path": "../../packages/ai"}, {"path": "../../packages/openai"}, {"path": "../../packages/provider-utils"}, {"path": "../../packages/svelte"}, {"path": "../../packages/ai"}]}